# -*- coding: utf-8 -*-
"""
Created on Thu Aug 29 11:15:20 2024

@author: devri.agustianto
"""

# Clear all variables
for name in dir():
    if not name.startswith('_'):
        del globals()[name]

import numpy as np
import lasio
import tkinter as tk
from tkinter import filedialog, simpledialog, ttk, messagebox
import matplotlib.pyplot as plt
from eeimpcalc import eeimpcalc  # Import the external module
import os
import pandas as pd
from tabulate import tabulate
from scipy import stats  # Import for linear regression

# COMPLATION OF FUNCTION
# Define keywords for broader log detection
log_keywords = {
    'DT': ['DT', 'DTCO', 'P-SONIC', 'P_SONIC'],  # P-wave slowness
    'DTS': ['DTS', 'DTSM', 'S-SONIC', 'S_SONIC'],  # S-wave slowness
    'PHIT': ['PHIT', 'PHID', 'PHI_D'],
    'PHIE': ['PHIE', 'PHIE_D'],
    'RHOB': ['RHOB', 'DEN', 'DENS', 'DENSITY', 'RHOZ', 'RHO'],  # Added more density log keywords
    'SWT': ['SWT', 'SW', 'WATER_SAT'],
    'SWE': ['SWE', 'SWE_D'],
    'DEPTH': ['DEPTH', 'MD', 'MEASURED_DEPTH'],
    'P-WAVE': ['P-WAVE', 'P_VELOCITY', 'VP'],
    'S-WAVE': ['S-WAVE', 'S_VELOCITY', 'VS'],
    'FLUID_CODE': ['FLUID_CODE', 'FLUID'],
    'FLUID_PETREL': ['FLUID_PETREL'],
    'LITHO_CODE': ['LITHO_CODE', 'LITHOLOGY','LITHO_PETREL'],
    'GR': ['GR', 'GAMMA_RAY', 'GR_LOG'],
    'NPHI': ['NPHI', 'NEUTRON', 'NEUTRON_POROSITY'],
    'KSOLID': ['KSOLID', 'K_SOLID'],
    'GSOLID': ['GSOLID', 'G_SOLID'],
    'KSAT': ['KSAT', 'K_SATURATION'],
    'GSAT': ['GSAT', 'G_SATURATION'],
    'KDRY': ['KDRY', 'K_DRY'],
    'GDRY': ['GDRY', 'G_DRY'],
    'VCL': ['VCL', 'VOL_WETCLAY', 'V_CLAY'],
    'RT': ['RT', 'RES', 'RESISTIVITY', 'ILD', 'LLD', 'AT90']
}

def load_multiple_las_files():
    """
    Load multiple LAS files and process the data.

    Returns:
    A list of lasio.LASFile objects.
    """
    root = tk.Tk()
    root.withdraw()  # Hide the root window
    file_paths = filedialog.askopenfilenames(title="Select LAS files", filetypes=[("LAS files", "*.las")])

    las_files = []
    for file_path in file_paths:
        try:
            las = lasio.read(file_path)
            # Store the file path for reference
            las.file_path = file_path
            las_files.append(las)
            print(f"Loaded file: {file_path}")
        except Exception as e:
            print(f"Error loading file {file_path}: {str(e)}")

    return las_files

def validate_essential_logs(las_files, log_keywords):
    """
    Validate that each LAS file contains the essential logs: Vp, Vs, and Density.

    Args:
        las_files: List of lasio.LASFile objects
        log_keywords: Dictionary mapping standardized names to possible mnemonics

    Returns:
        Dictionary with validation results for each file
    """
    validation_results = {}

    for las in las_files:
        well_name = las.well.WELL.value
        file_path = getattr(las, 'file_path', 'Unknown file path')
        file_name = os.path.basename(file_path)

        # Find columns for this well
        columns = find_default_columns(las, log_keywords)

        # Check for essential logs
        missing_logs = []

        # Check for P-wave velocity (either direct or via slowness)
        has_p_wave = False
        if columns.get('P-WAVE') and columns['P-WAVE'] in las.curves:
            has_p_wave = True
        elif columns.get('DT') and columns['DT'] in las.curves:
            has_p_wave = True

        if not has_p_wave:
            missing_logs.append('P-wave velocity (Vp)')

        # Check for S-wave velocity (either direct or via slowness)
        has_s_wave = False
        if columns.get('S-WAVE') and columns['S-WAVE'] in las.curves:
            has_s_wave = True
        elif columns.get('DTS') and columns['DTS'] in las.curves:
            has_s_wave = True

        if not has_s_wave:
            missing_logs.append('S-wave velocity (Vs)')

        # Check for Density
        has_density = False
        if columns.get('RHOB') and columns['RHOB'] in las.curves:
            has_density = True

        if not has_density:
            missing_logs.append('Density')

        # Store validation result
        validation_results[well_name] = {
            'file_path': file_path,
            'file_name': file_name,
            'missing_logs': missing_logs,
            'is_valid': len(missing_logs) == 0
        }

    return validation_results

def generate_validation_report(validation_results):
    """
    Generate a formatted report of the validation results.

    Args:
        validation_results: Dictionary with validation results from validate_essential_logs()
    """
    print("\n" + "="*80)
    print("LAS FILE VALIDATION REPORT")
    print("="*80)

    valid_files = 0
    total_files = len(validation_results)

    print("\nEssential logs required: P-wave velocity (Vp), S-wave velocity (Vs), Density\n")

    # Prepare data for tabulate
    table_data = []
    for well_name, result in validation_results.items():
        status = "✓ VALID" if result['is_valid'] else "✗ INVALID"
        missing = ", ".join(result['missing_logs']) if result['missing_logs'] else "None"
        table_data.append([well_name, result['file_name'], missing, status])

        if result['is_valid']:
            valid_files += 1

    # Print table
    headers = ["Well Name", "File Name", "Missing Logs", "Status"]
    print(tabulate(table_data, headers=headers, tablefmt="grid"))

    print("\nSummary: {}/{} files contain all essential logs.".format(valid_files, total_files))
    print("="*80 + "\n")

    # Return a summary for potential use elsewhere
    return {
        'valid_files': valid_files,
        'total_files': total_files,
        'invalid_files': total_files - valid_files
    }

def categorize_log_curves(las_files):
    """
    Categorize log curves across all LAS files.

    Args:
        las_files: List of lasio.LASFile objects

    Returns:
        Dictionary with categorized log curves
    """
    # Initialize categories
    categories = {}

    # Process each LAS file
    for las in las_files:
        well_name = las.well.WELL.value

        for curve in las.curves:
            mnemonic = curve.mnemonic
            unit = curve.unit
            descr = curve.descr if hasattr(curve, 'descr') else ''

            # Create a standardized key for this curve
            curve_key = mnemonic.upper()

            # Initialize if this is a new curve type
            if curve_key not in categories:
                categories[curve_key] = {
                    'mnemonics': set(),
                    'units': set(),
                    'descriptions': set(),
                    'count': 0,
                    'files': set()
                }

            # Update category information
            categories[curve_key]['mnemonics'].add(mnemonic)
            categories[curve_key]['units'].add(unit)
            categories[curve_key]['descriptions'].add(descr)
            categories[curve_key]['count'] += 1
            categories[curve_key]['files'].add(well_name)

    # Convert sets to lists for easier display
    for key in categories:
        categories[key]['mnemonics'] = list(categories[key]['mnemonics'])
        categories[key]['units'] = list(filter(None, categories[key]['units']))  # Remove empty units
        categories[key]['descriptions'] = list(filter(None, categories[key]['descriptions']))  # Remove empty descriptions
        categories[key]['files'] = list(categories[key]['files'])

    return categories

def display_log_inventory(categories):
    """
    Display the inventory of log curves.

    Args:
        categories: Dictionary with categorized log curves from categorize_log_curves()
    """
    print("\n" + "="*80)
    print("LOG CURVE INVENTORY")
    print("="*80)

    # Sort categories by count (descending)
    sorted_categories = sorted(categories.items(), key=lambda x: x[1]['count'], reverse=True)

    # Prepare data for tabulate
    table_data = []
    for key, info in sorted_categories:
        common_units = ", ".join(info['units'][:2]) if info['units'] else "N/A"  # Show only first 2 units for brevity
        common_desc = ", ".join(info['descriptions'][:1]) if info['descriptions'] else "N/A"  # Show only first description
        file_count = len(info['files'])
        file_list = ", ".join(info['files'][:3])  # Show only first 3 files for brevity
        if file_count > 3:
            file_list += f" and {file_count - 3} more"

        table_data.append([key, info['count'], common_units, common_desc, file_list])

    # Print table
    headers = ["Log Type", "Count", "Common Units", "Description", "Files"]
    print(tabulate(table_data, headers=headers, tablefmt="grid"))

    print("\nTotal unique log types: {}".format(len(categories)))
    print("="*80 + "\n")

    return categories

def log_available_curves(las):
    """
    Log available curves in the LAS file for diagnostic purposes.
    """
    print(f"Available curves for well {las.well.WELL.value}:")
    for curve in las.curves:
        print(f"{curve.mnemonic}: {curve.unit}")


def find_default_columns(las, keywords):
    """
    Find the default columns for specific logs based on the provided keywords.
    This version includes detailed logging for troubleshooting.
    """
    default_columns = {}
    for keyword, aliases in keywords.items():
        found = False
        for alias in aliases:
            for curve in las.curves:
                # Case-insensitive comparison
                if alias.upper() == curve.mnemonic.upper():
                    default_columns[keyword] = curve.mnemonic
                    print(f"Found curve {curve.mnemonic} for alias {alias}")
                    found = True
                    break
            if found:
                break
        if not found:
            default_columns[keyword] = None
            print(f"Curve for {keyword} not found among aliases {aliases}")
    return default_columns


def nanaware_corrcoef(x, y):
    """
    Calculate correlation coefficient while properly handling NaN values.
    This function mimics MATLAB's corr function with 'rows' set to 'complete'.
    """
    mask = ~np.isnan(x) & ~np.isnan(y)
    if np.sum(mask) < 2:
        return np.nan
    x_clean = x[mask]
    y_clean = y[mask]
    if np.all(x_clean == x_clean[0]) or np.all(y_clean == y_clean[0]):
        return np.nan
    return np.corrcoef(x_clean, y_clean)[0, 1]

def find_nearest_index(array, value):
    """Find the index of the nearest value in an array."""
    return np.abs(array - value).argmin()

def merge_well_data(las_files, columns, target_log, depth_ranges):
    """
    Merge data from multiple wells into a single array.
    """
    merged_depth = []
    merged_dt = []
    merged_dts = []
    merged_rhob = []
    merged_target = []

    for las in las_files:
        well_name = las.well.WELL.value
        top_depth, bottom_depth = depth_ranges[well_name]

        print(f"Processing well: {well_name}")
        print(f"Available columns: {columns}")
        print(f"Target log: {target_log}")
        print(f"Available curves in LAS file: {list(las.curves.keys())}")

        depth = np.array(las[columns['DEPTH']].data)
        dt = np.array(las[columns['DT']].data)
        dts = np.array(las[columns['DTS']].data)
        rhob = np.array(las[columns['RHOB']].data)

        # Check if target_log is in columns, if not, try to find it directly in the LAS file
        if target_log in columns and columns[target_log] in las.curves.keys():
            target = np.array(las[columns[target_log]].data)
        elif target_log in las.curves.keys():
            target = np.array(las[target_log].data)
        else:
            print(f"Warning: Target log '{target_log}' not found in well {well_name}. Skipping this well.")
            continue

        # Find nearest indices for top and bottom depths
        top_index = find_nearest_index(depth, top_depth)
        bottom_index = find_nearest_index(depth, bottom_depth)

        # Ensure top_index is smaller than bottom_index
        if top_index > bottom_index:
            top_index, bottom_index = bottom_index, top_index

        # Slice the arrays using the indices
        merged_depth.extend(depth[top_index:bottom_index+1])
        merged_dt.extend(dt[top_index:bottom_index+1])
        merged_dts.extend(dts[top_index:bottom_index+1])
        merged_rhob.extend(rhob[top_index:bottom_index+1])
        merged_target.extend(target[top_index:bottom_index+1])

    if not merged_depth:
        print("No data was merged. Check if target log exists in any of the wells.")
        return None, None, None, None, None

    return np.array(merged_depth), np.array(merged_dt), np.array(merged_dts), np.array(merged_rhob), np.array(merged_target)

def get_calcmethod_and_k_method():
    """
    Prompt the user to select the calcmethod for EEI calculation and how to determine k value.
    """
    root = tk.Tk()
    root.withdraw()
    calcmethod = simpledialog.askinteger("Input", "Enter the calcmethod for EEI calculation:\n1: EEI using Vp, Vs, density\n2: EEI using AI, SI, density\n3: EEI using AI, SI, with omitted density", minvalue=1, maxvalue=3)
    if calcmethod not in [1, 2, 3]:
        print("Invalid calcmethod. Using default value 3.")
        calcmethod = 3
    
    # Ask user how to determine k value
    k_method = simpledialog.askinteger("Input", "How would you like to determine k value?\n1: Calculate from average of logs (Vs/Vp)²\n2: Enter a constant value manually", minvalue=1, maxvalue=2)
    
    # If user chose to enter a constant value
    k_value = None
    if k_method == 2:
        k_value = simpledialog.askfloat("Input", "Enter constant k value (typically between 0.1 and 0.5):", minvalue=0.01, maxvalue=1.0)
        if k_value is None:
            print("Invalid k value. Using default method (calculate from logs).")
            k_method = 1
    
    return calcmethod, k_method, k_value

def calculate_eei_optimum_angle(las, actual_base_log_mnemonics, target_log_actual_mnemonic, top_depth, bottom_depth, calcmethod, k_method=1, k_value=None):
    """
    Calculate EEI using the specified calcmethod for angles from -90 to 90 and find the optimum angle.
    Uses actual mnemonics specific to the 'las' file.
    actual_base_log_mnemonics is a dict: {'DEPTH': 'actual_depth_name', 'DT': 'actual_dt_name', ...}
    target_log_actual_mnemonic is the string name of the target curve, e.g. 'GR' or 'GAMMA_RAY'
    k_method: 1 for calculating k from logs, 2 for using constant k_value
    k_value: Constant k value to use if k_method is 2
    """

    depth = np.array(las[actual_base_log_mnemonics['DEPTH']].data)
    dt = np.array(las[actual_base_log_mnemonics['DT']].data)
    dts = np.array(las[actual_base_log_mnemonics['DTS']].data)
    rhob = np.array(las[actual_base_log_mnemonics['RHOB']].data)
    target = np.array(las[target_log_actual_mnemonic].data)

    # Find nearest indices for top and bottom depths
    top_index = find_nearest_index(depth, top_depth)
    bottom_index = find_nearest_index(depth, bottom_depth)

    # Ensure top_index is smaller than bottom_index
    if top_index > bottom_index:
        top_index, bottom_index = bottom_index, top_index

    # Slice the arrays using the indices
    depth = depth[top_index:bottom_index+1]
    dt = dt[top_index:bottom_index+1]
    dts = dts[top_index:bottom_index+1]
    rhob = rhob[top_index:bottom_index+1]
    target = target[top_index:bottom_index+1]

    # Convert slowness to velocity (assuming microseconds/ft to m/s conversion)
    pvel = 304800 / dt  # Convert microseconds/ft to m/s
    svel = 304800 / dts  # Convert microseconds/ft to m/s

    # Determine k value based on method selected
    if k_method == 1:  # Calculate from logs
        k = np.nanmean((svel/pvel)**2)
        print(f"Calculated k value from logs: {k:.4f}")
    else:  # Use constant value
        k = k_value
        print(f"Using constant k value: {k:.4f}")

    angles = range(-90, 91)
    correlations = []

    for angle in angles:
        eei, _, _ = eeimpcalc(pvel, svel, rhob, angle, k, calcmethod=calcmethod)
        correlation = nanaware_corrcoef(eei, target)
        correlations.append(correlation)

    # Find the optimum angle
    if np.all(np.isnan(correlations)):
        print("All correlations are NaN. Unable to find optimum angle.")
        return None, None, angles, correlations

    optimum_angle = angles[np.nanargmax(correlations)]
    max_correlation = np.nanmax(correlations)

    return optimum_angle, max_correlation, angles, correlations

def calculate_eei_optimum_angle_merged(depth, dt, dts, rhob, target, calcmethod, k_method=1, k_value=None):
    """
    Calculate EEI using calcmethod 3 for angles from -90 to 90 and find the optimum angle for merged data.
    k_method: 1 for calculating k from logs, 2 for using constant k_value
    k_value: Constant k value to use if k_method is 2
    """
    # Convert slowness to velocity (assuming microseconds/ft to m/s conversion)
    pvel = 304800 / dt  # Convert microseconds/ft to m/s
    svel = 304800 / dts  # Convert microseconds/ft to m/s

    # Determine k value based on method selected
    if k_method == 1:
        k = np.nanmean((svel/pvel)**2)
        print(f"Calculated k value from logs (merged): {k:.4f}")
    else:
        k = k_value
        print(f"Using constant k value (merged): {k:.4f}")

    angles = range(-90, 91)
    correlations = []

    for angle in angles:
        eei, _, _ = eeimpcalc(pvel, svel, rhob, angle, k, calcmethod=calcmethod)
        correlation = nanaware_corrcoef(eei, target)
        correlations.append(correlation)

    # Find the optimum angle
    if np.all(np.isnan(correlations)):
        print("All correlations are NaN. Unable to find optimum angle.")
        return None, None, angles, correlations

    optimum_angle = angles[np.nanargmax(correlations)]
    max_correlation = np.nanmax(correlations)

    return optimum_angle, max_correlation, angles, correlations

def calculate_eei(las, actual_base_log_mnemonics, target_log_actual_mnemonic, vcl_actual_mnemonic, top_depth, bottom_depth, angle, calcmethod, k_method=1, k_value=None):
    """
    Calculate EEI using the given angle and clip results based on percentiles.
    Uses actual mnemonics specific to the 'las' file.
    actual_base_log_mnemonics is a dict: {'DEPTH': 'actual_depth_name', 'DT': 'actual_dt_name', ...}
    target_log_actual_mnemonic is the string name of the target curve.
    vcl_actual_mnemonic is the string name of the VCL curve, or None.
    """
    depth = np.array(las[actual_base_log_mnemonics['DEPTH']].data)
    dt = np.array(las[actual_base_log_mnemonics['DT']].data)
    dts = np.array(las[actual_base_log_mnemonics['DTS']].data)
    rhob = np.array(las[actual_base_log_mnemonics['RHOB']].data)
    target = np.array(las[target_log_actual_mnemonic].data)

    # Find nearest indices for top and bottom depths
    top_index = find_nearest_index(depth, top_depth)
    bottom_index = find_nearest_index(depth, bottom_depth)

    # Ensure top_index is smaller than bottom_index
    if top_index > bottom_index:
        top_index, bottom_index = bottom_index, top_index

    # Slice the arrays using the indices
    depth = depth[top_index:bottom_index+1]
    dt = dt[top_index:bottom_index+1]
    dts = dts[top_index:bottom_index+1]
    rhob = rhob[top_index:bottom_index+1]
    target = target[top_index:bottom_index+1]

    # Convert slowness to velocity (assuming microseconds/ft to m/s conversion)
    pvel = 304800 / dt  # Convert microseconds/ft to m/s
    svel = 304800 / dts  # Convert microseconds/ft to m/s


    # Determine k value based on method selected
    if k_method == 1:
        k = np.nanmean((svel/pvel)**2)
        # Optionally print for debug: print(f"Calculated k value from logs (final EEI): {k:.4f}")
    else:
        k = k_value
        # Optionally print for debug: print(f"Using constant k value (final EEI): {k:.4f}")


    # Calculate EEI
    eei, _, _ = eeimpcalc(pvel, svel, rhob, angle, k, calcmethod=calcmethod)

     # Step 1: Min-Max Normalization of EEI
    eei_min = np.nanmin(eei)
    eei_max = np.nanmax(eei)

    # Avoid division by zero if min and max are the same
    if eei_max != eei_min:
        norm_eei = (eei - eei_min) / (eei_max - eei_min)
    else:
        norm_eei = eei  # No scaling needed if all values are the same

    # Step 2: Scale the normalized EEI to match the target log range
    target_min = np.nanmin(target)
    target_max = np.nanmax(target)

    normalized_eei = norm_eei * (target_max - target_min) + target_min

    # Get VOL_WETCLAY data
    vol_wetclay = None
    if vcl_actual_mnemonic and vcl_actual_mnemonic in las.curves:
        vol_wetclay = np.array(las[vcl_actual_mnemonic].data)[top_index:bottom_index+1]

    # Warning for missing VCL is better handled where find_default_columns is called.
    # if vol_wetclay is None:
    #     print(f"Info: VOL_WETCLAY (mnemonic: {vcl_actual_mnemonic}) not available or not found for well {las.well.WELL.value} in calculate_eei. Proceeding without VOL_WETCLAY data.")

    # Remove NaN and Inf values, and ensure consistent lengths
    mask = np.isfinite(depth) & np.isfinite(target) & np.isfinite(normalized_eei)
    if vol_wetclay is not None:
        mask = mask & np.isfinite(vol_wetclay)

    depth = depth[mask]
    target = target[mask]
    normalized_eei = normalized_eei[mask]
    if vol_wetclay is not None:
        vol_wetclay = vol_wetclay[mask]

    # Check if arrays are empty or contain only NaNs
    if depth.size == 0 or target.size == 0 or normalized_eei.size == 0:
        print(f"No valid data to process for well {las.well.WELL.value}. Skipping EEI calculation.")
        return None, None, None, None

    # Use np.nanpercentile to compute percentiles while ignoring NaNs
    eei_low, eei_high = np.nanpercentile(normalized_eei, [2, 98])
    target_low, target_high = np.nanpercentile(target, [2, 98])

    # Clip normalized_eei and target based on 2nd and 98th percentiles
    normalized_eei = np.clip(normalized_eei, eei_low, eei_high)
    target = np.clip(target, target_low, target_high)

    return depth, target, normalized_eei, vol_wetclay

def plot_eei_vs_target(all_wells_data, target_log, depth_ranges):
    """
    Create separate three-column plots for each well:
    1. Depth vs VOL_WETCLAY (if available)
    2. Depth vs Target Log and EEI(optimum angle)
    3. Crossplot of EEI vs Target Log
    """
    # Ask user for x-axis range for target log and EEI in the second column
    root = tk.Tk()
    root.withdraw()

    # Create a custom dialog that allows empty inputs
    class CustomRangeDialog(tk.simpledialog.Dialog):
        def __init__(self, parent, title, target_log):
            self.target_log = target_log
            self.x_min = None
            self.x_max = None
            super().__init__(parent, title)

        def body(self, master):
            ttk.Label(master, text=f"Enter x-axis range for {self.target_log} and EEI:").grid(row=0, column=0, columnspan=2, sticky="w", pady=(0, 10))
            ttk.Label(master, text="Minimum value (leave empty for auto):").grid(row=1, column=0, sticky="w", pady=5)
            ttk.Label(master, text="Maximum value (leave empty for auto):").grid(row=2, column=0, sticky="w", pady=5)

            self.min_entry = ttk.Entry(master)
            self.min_entry.grid(row=1, column=1, padx=5, pady=5)

            self.max_entry = ttk.Entry(master)
            self.max_entry.grid(row=2, column=1, padx=5, pady=5)

            return self.min_entry  # Initial focus

        def validate(self):
            min_val = self.min_entry.get().strip()
            max_val = self.max_entry.get().strip()

            try:
                if min_val:
                    self.x_min = float(min_val)
                if max_val:
                    self.x_max = float(max_val)

                # Ensure min is less than max if both are provided
                if self.x_min is not None and self.x_max is not None and self.x_min >= self.x_max:
                    messagebox.showerror("Invalid Range", "Minimum value must be less than maximum value.")
                    return False

                return True
            except ValueError:
                messagebox.showerror("Invalid Input", "Please enter valid numbers or leave fields empty.")
                return False

        def apply(self):
            # Values are already stored in self.x_min and self.x_max
            pass

    # Show the custom dialog
    dialog = CustomRangeDialog(root, "X-Axis Range", target_log)
    x_min = dialog.x_min
    x_max = dialog.x_max

    for well_data in all_wells_data:
        well_name = well_data['well_name']
        depth = well_data['depth']
        target = well_data['target']
        normalized_eei = well_data['normalized_eei']
        angle = well_data['angle']
        vol_wetclay = well_data.get('vol_wetclay')

        # Check if we have all necessary data
        if depth is None or target is None or normalized_eei is None:
            print(f"Skipping well {well_name} due to missing data.")
            continue

        # Calculate correlation, automatically omitting NaN values
        correlation = nanaware_corrcoef(normalized_eei, target)

        # Get the analysis depth range and add buffer
        top_depth, bottom_depth = depth_ranges[well_name]
        y_min, y_max = top_depth - 30, bottom_depth + 30

        # Determine the number of subplots based on available data
        if vol_wetclay is not None:
            fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(20, 10))
        else:
            fig, (ax2, ax3) = plt.subplots(1, 2, figsize=(15, 10))
            print(f"Warning: VOL_WETCLAY not available for well {well_name}, plotting only 2 columns.")

        fig.suptitle(f'{well_name}: EEI vs {target_log} Analysis', fontsize=16)

        # First column: Depth vs VOL_WETCLAY (if available)
        if vol_wetclay is not None:
            valid_vcl_mask = ~np.isnan(vol_wetclay)
            ax1.plot(vol_wetclay[valid_vcl_mask], depth[valid_vcl_mask], label='VOL_WETCLAY', color='green')
            ax1.set_xlabel('VOL_WETCLAY')
            ax1.set_ylabel('Depth')
            ax1.set_ylim(y_max, y_min)  # Correct order for depth to increase downwards
            ax1.set_xlim(0, 1)  # Set x-axis limits for VOL_WETCLAY
            ax1.axhline(y=top_depth, color='r', linestyle='--', label='Top')
            ax1.axhline(y=bottom_depth, color='b', linestyle='--', label='Bottom')
            ax1.legend()

        # Second column: Depth vs Target Log and Normalized EEI
        valid_mask = ~np.isnan(target) & ~np.isnan(normalized_eei) & ~np.isnan(depth)
        ax2.plot(np.ma.masked_array(target, ~valid_mask),
                 np.ma.masked_array(depth, ~valid_mask),
                 label=target_log, color='blue')
        ax2.plot(np.ma.masked_array(normalized_eei, ~valid_mask),
                 np.ma.masked_array(depth, ~valid_mask),
                 label=f'EEI ({angle:.1f}°)', color='red')
        ax2.set_xlabel('Value')
        ax2.set_ylabel('Depth')
        ax2.set_ylim(y_max, y_min)  # Correct order for depth to increase downwards

        # Set x-axis limits based on user input or percentiles
        if x_min is not None and x_max is not None:
            # Both min and max provided by user
            ax2.set_xlim(x_min, x_max)
        elif x_min is not None:
            # Only min provided, calculate max from 98th percentile
            valid_data = np.concatenate([target[valid_mask], normalized_eei[valid_mask]])
            data_max = np.nanpercentile(valid_data, 98)
            ax2.set_xlim(x_min, data_max)
        elif x_max is not None:
            # Only max provided, calculate min from 2nd percentile
            valid_data = np.concatenate([target[valid_mask], normalized_eei[valid_mask]])
            data_min = np.nanpercentile(valid_data, 2)
            ax2.set_xlim(data_min, x_max)
        else:
            # No user input, use 2nd and 98th percentiles for auto-scaling
            valid_data = np.concatenate([target[valid_mask], normalized_eei[valid_mask]])
            data_min = np.nanpercentile(valid_data, 2)
            data_max = np.nanpercentile(valid_data, 98)
            ax2.set_xlim(data_min, data_max)
        ax2.axhline(y=top_depth, color='r', linestyle='--', label='Top')
        ax2.axhline(y=bottom_depth, color='b', linestyle='--', label='Bottom')
        ax2.legend()

        # Third column: Scatter plot of Normalized EEI vs Target Log
        ax3.scatter(np.ma.masked_array(normalized_eei, ~valid_mask),
                    np.ma.masked_array(target, ~valid_mask),
                    alpha=0.5)
        ax3.set_xlabel(f'EEI ({angle:.1f}°)')
        ax3.set_ylabel(target_log)

        # Set x and y limits for the third column using the 2nd and 98th percentiles
        valid_data = np.concatenate([target[valid_mask], normalized_eei[valid_mask]])
        overall_min = np.nanpercentile(valid_data, 2)
        overall_max = np.nanpercentile(valid_data, 98)
        ax3.set_xlim(overall_min, overall_max)
        ax3.set_ylim(overall_min, overall_max)

        # Calculate linear regression
        x_for_regression = normalized_eei[valid_mask]
        y_for_regression = target[valid_mask]
        if len(x_for_regression) > 1:  # Need at least 2 points for regression
            # Ensure no NaN values in the data
            reg_mask = ~np.isnan(x_for_regression) & ~np.isnan(y_for_regression)
            if np.sum(reg_mask) > 1:  # Still need at least 2 valid points after removing NaNs
                regression = stats.linregress(x_for_regression[reg_mask], y_for_regression[reg_mask])
                # Plot regression line
                x_line = np.array([overall_min, overall_max])
                y_line = regression.slope * x_line + regression.intercept
                ax3.plot(x_line, y_line, 'r-', linewidth=2, label='Linear regression')

                # Add regression equation and R² to the plot
                equation = f'y = {regression.slope:.4f}x + {regression.intercept:.4f}'
                r_squared = regression.rvalue**2
                ax3.text(0.05, 0.85, equation, transform=ax3.transAxes, verticalalignment='top')
                ax3.text(0.05, 0.75, f'R² = {r_squared:.4f}', transform=ax3.transAxes, verticalalignment='top')

        # Add a diagonal line (y = x) for reference
        ax3.plot([overall_min, overall_max], [overall_min, overall_max], 'k--', alpha=0.5, label='y = x')

        # Add correlation value to the plot
        ax3.text(0.05, 0.95, f'Correlation: {correlation:.4f}',
                 transform=ax3.transAxes, verticalalignment='top')

        # Add legend
        ax3.legend(loc='lower right')

        # Add correlation value to the plot title
        fig.suptitle(f'{well_name}: EEI vs {target_log} Analysis\nCorrelation: {correlation:.4f}', fontsize=16)

        plt.tight_layout()
        plt.show()

def individual_well_analysis(las_files, log_keywords_for_finding_cols, target_log_generic_name, depth_ranges, calcmethod, k_method, k_value, alternative_mnemonics=None):
    """
    Perform individual well analysis.

    Args:
        las_files: List of LAS file objects
        log_keywords_for_finding_cols: Dictionary mapping generic names to possible mnemonics
        target_log_generic_name: Generic name of the target log
        depth_ranges: Dictionary mapping well names to (top_depth, bottom_depth) tuples
        calcmethod: Method to use for EEI calculation
        alternative_mnemonics: Dictionary to track alternative mnemonics selected by the user

    Returns:
        Tuple of (all_wells_results, all_wells_data)
    """
    all_wells_results = []
    all_wells_data = []

    # Initialize alternative_mnemonics if not provided
    if alternative_mnemonics is None:
        alternative_mnemonics = {}

    required_base_generic_logs = ['DEPTH', 'DT', 'DTS', 'RHOB']

    for las in las_files:
        well_name = las.well.WELL.value
        top_depth, bottom_depth = depth_ranges[well_name]

        # Find actual mnemonics for this specific well
        current_well_columns = find_default_columns(las, log_keywords_for_finding_cols)

        actual_mnemonics_for_base_logs = {}
        missing_base_logs_info = []
        for generic_name in required_base_generic_logs:
            actual_mnemonic = current_well_columns.get(generic_name)
            if actual_mnemonic is None or actual_mnemonic not in las.curves:
                missing_base_logs_info.append(f"{generic_name} (searched aliases: {log_keywords_for_finding_cols.get(generic_name, [])})")
            else:
                actual_mnemonics_for_base_logs[generic_name] = actual_mnemonic

        if missing_base_logs_info:
            print(f"Warning: Missing or invalid base logs ({', '.join(missing_base_logs_info)}) for well {well_name}. Skipping this well for individual analysis.")
            all_wells_data.append({'well_name': well_name, 'depth': None, 'target': None, 'normalized_eei': None, 'angle': None, 'vol_wetclay': None})
            all_wells_results.append({'well_name': well_name, 'optimum_angle': None, 'max_correlation': None, 'top_depth': top_depth, 'bottom_depth': bottom_depth})
            continue

        target_log_actual_mnemonic = current_well_columns.get(target_log_generic_name)
        # If generic name itself is a curve (e.g. user typed 'GR' and 'GR' exists, but 'GR' is not in log_keywords['GR'])
        if target_log_actual_mnemonic is None and target_log_generic_name in las.curves:
            target_log_actual_mnemonic = target_log_generic_name
            print(f"Info: Target log '{target_log_generic_name}' used directly as mnemonic for well {well_name}.")

        # Check if target log is missing and offer interactive fallback selection
        if target_log_actual_mnemonic is None or target_log_actual_mnemonic not in las.curves:
            # Check if we already have an alternative for this well
            well_key = f"{well_name}:{target_log_generic_name}"
            if well_key in alternative_mnemonics:
                alternative = alternative_mnemonics[well_key]
                if alternative is not None:
                    target_log_actual_mnemonic = alternative
                    print(f"Info: Using previously selected alternative '{alternative}' for target log '{target_log_generic_name}' in well '{well_name}'.")
                else:
                    print(f"Info: Well '{well_name}' was previously marked to be skipped for target log '{target_log_generic_name}'.")
                    all_wells_data.append({'well_name': well_name, 'depth': None, 'target': None, 'normalized_eei': None, 'angle': None, 'vol_wetclay': None})
                    all_wells_results.append({'well_name': well_name, 'optimum_angle': None, 'max_correlation': None, 'top_depth': top_depth, 'bottom_depth': bottom_depth})
                    continue
            else:
                # Display dialog for selecting an alternative
                print(f"Warning: Target log '{target_log_generic_name}' (resolved mnemonic '{target_log_actual_mnemonic}', searched aliases: {log_keywords_for_finding_cols.get(target_log_generic_name, [target_log_generic_name])}) not found in well {well_name}.")
                alternative = select_alternative_mnemonic(las, target_log_generic_name, well_name)

                # Store the selected alternative (or None if skipped)
                alternative_mnemonics[well_key] = alternative

                if alternative is None:
                    print(f"Info: User chose to skip well '{well_name}' for target log '{target_log_generic_name}'.")
                    all_wells_data.append({'well_name': well_name, 'depth': None, 'target': None, 'normalized_eei': None, 'angle': None, 'vol_wetclay': None})
                    all_wells_results.append({'well_name': well_name, 'optimum_angle': None, 'max_correlation': None, 'top_depth': top_depth, 'bottom_depth': bottom_depth})
                    continue
                else:
                    target_log_actual_mnemonic = alternative
                    print(f"Info: Using alternative '{alternative}' for target log '{target_log_generic_name}' in well '{well_name}'.")

        vcl_actual_mnemonic = current_well_columns.get('VCL') # VCL is optional, might be None. find_default_columns would have printed if not found.

        optimum_angle, max_correlation, angles, correlations = calculate_eei_optimum_angle(
            las, actual_mnemonics_for_base_logs, target_log_actual_mnemonic,
            top_depth, bottom_depth, calcmethod, k_method, k_value
        )

        if optimum_angle is None or max_correlation is None: # Should be caught by checks inside calculate_eei_optimum_angle too
            print(f"Skipping well {well_name} due to invalid data from optimum angle calculation (e.g., all NaN correlations).")
            all_wells_data.append({'well_name': well_name, 'depth': None, 'target': None, 'normalized_eei': None, 'angle': None, 'vol_wetclay': None})
            all_wells_results.append({'well_name': well_name, 'optimum_angle': None, 'max_correlation': None, 'top_depth': top_depth, 'bottom_depth': bottom_depth})
            continue

        print(f"Optimum angle for Well {well_name}: {optimum_angle}°")
        print(f"Maximum correlation coefficient with {target_log_generic_name} (using {target_log_actual_mnemonic}): {max_correlation:.4f}")

        # Plot EEI-Target Log Correlation vs Angle
        plt.figure(figsize=(12, 6))
        plt.plot(angles, correlations)
        plt.axvline(x=optimum_angle, color='r', linestyle='--', label=f'Optimum Angle: {optimum_angle}°')
        plt.xlabel('Angle (degrees)')
        plt.ylabel('Correlation Coefficient')
        plt.title(f'EEI-{target_log_generic_name} Correlation vs Angle for Well: {well_name}\nDepth range: {top_depth:.2f} - {bottom_depth:.2f}')
        plt.legend()
        plt.grid(True)
        plt.show()

        # Calculate EEI using the optimum angle
        depth, target, normalized_eei, vol_wetclay = calculate_eei(
            las, actual_mnemonics_for_base_logs, target_log_actual_mnemonic, vcl_actual_mnemonic,
            top_depth, bottom_depth, optimum_angle, calcmethod, k_method, k_value
        )

        all_wells_results.append({
            'well_name': well_name,
            'optimum_angle': optimum_angle,
            'max_correlation': max_correlation,
            'top_depth': top_depth,
            'bottom_depth': bottom_depth
        })

        all_wells_data.append({
            'well_name': well_name,
            'depth': depth,
            'target': target,
            'normalized_eei': normalized_eei,
            'angle': optimum_angle,
            'vol_wetclay': vol_wetclay
        })

    return all_wells_results, all_wells_data

def merged_well_analysis(las_files, log_keywords_for_finding_cols, target_log_generic_name, depth_ranges, calcmethod, k_method, k_value, alternative_mnemonics=None):
    """
    Perform merged well analysis.

    Args:
        las_files: List of LAS file objects
        log_keywords_for_finding_cols: Dictionary mapping generic names to possible mnemonics
        target_log_generic_name: Generic name of the target log
        depth_ranges: Dictionary mapping well names to (top_depth, bottom_depth) tuples
        calcmethod: Method to use for EEI calculation
        alternative_mnemonics: Dictionary to track alternative mnemonics selected by the user

    Returns:
        List of well data for plotting
    """
    merged_depth_list = []
    merged_dt_list = []
    merged_dts_list = []
    merged_rhob_list = []
    merged_target_list = []

    # Initialize alternative_mnemonics if not provided
    if alternative_mnemonics is None:
        alternative_mnemonics = {}

    required_base_generic_logs = ['DEPTH', 'DT', 'DTS', 'RHOB']
    processed_wells_for_merge = 0

    for las in las_files:
        well_name = las.well.WELL.value
        top_depth, bottom_depth = depth_ranges[well_name]

        current_well_cols = find_default_columns(las, log_keywords_for_finding_cols)

        actual_mnemonics_for_merge = {}
        missing_logs_for_this_well_merge = []
        for generic_name in required_base_generic_logs:
            mnemonic = current_well_cols.get(generic_name)
            if mnemonic is None or mnemonic not in las.curves:
                missing_logs_for_this_well_merge.append(f"{generic_name} (searched aliases: {log_keywords_for_finding_cols.get(generic_name, [])})")
            else:
                actual_mnemonics_for_merge[generic_name] = mnemonic

        # Check for missing required base logs (these can't be substituted)
        if any(log in missing_logs_for_this_well_merge for log in required_base_generic_logs):
            print(f"Warning: For merged analysis, well {well_name} is missing required base logs: {', '.join(missing_logs_for_this_well_merge)}. Skipping this well's data for merge.")
            continue # Skip this well for merging

        target_actual_mnemonic_for_merge = current_well_cols.get(target_log_generic_name)
        if target_actual_mnemonic_for_merge is None and target_log_generic_name in las.curves: # Direct check
             target_actual_mnemonic_for_merge = target_log_generic_name
             print(f"Info: Target log '{target_log_generic_name}' used directly as mnemonic for well {well_name} in merge.")

        # Check if target log is missing and offer interactive fallback selection
        if target_actual_mnemonic_for_merge is None or target_actual_mnemonic_for_merge not in las.curves:
            # Check if we already have an alternative for this well
            well_key = f"{well_name}:{target_log_generic_name}"
            if well_key in alternative_mnemonics:
                alternative = alternative_mnemonics[well_key]
                if alternative is not None:
                    target_actual_mnemonic_for_merge = alternative
                    print(f"Info: Using previously selected alternative '{alternative}' for target log '{target_log_generic_name}' in well '{well_name}' for merged analysis.")
                else:
                    print(f"Info: Well '{well_name}' was previously marked to be skipped for target log '{target_log_generic_name}'. Skipping for merged analysis.")
                    continue
            else:
                # Display dialog for selecting an alternative
                print(f"Warning: Target log '{target_log_generic_name}' (resolved mnemonic '{target_actual_mnemonic_for_merge}', searched aliases: {log_keywords_for_finding_cols.get(target_log_generic_name, [target_log_generic_name])}) not found in well {well_name}.")
                alternative = select_alternative_mnemonic(las, target_log_generic_name, well_name)

                # Store the selected alternative (or None if skipped)
                alternative_mnemonics[well_key] = alternative

                if alternative is None:
                    print(f"Info: User chose to skip well '{well_name}' for target log '{target_log_generic_name}' in merged analysis.")
                    continue
                else:
                    target_actual_mnemonic_for_merge = alternative
                    print(f"Info: Using alternative '{alternative}' for target log '{target_log_generic_name}' in well '{well_name}' for merged analysis.")

        depth_data = np.array(las[actual_mnemonics_for_merge['DEPTH']].data)
        dt_data = np.array(las[actual_mnemonics_for_merge['DT']].data)
        dts_data = np.array(las[actual_mnemonics_for_merge['DTS']].data)
        rhob_data = np.array(las[actual_mnemonics_for_merge['RHOB']].data)
        target_data = np.array(las[target_actual_mnemonic_for_merge].data)

        # Find nearest indices for top and bottom depths
        top_index = find_nearest_index(depth_data, top_depth)
        bottom_index = find_nearest_index(depth_data, bottom_depth)

        # Ensure top_index is smaller than bottom_index
        if top_index > bottom_index:
            top_index, bottom_index = bottom_index, top_index

        # Slice the arrays using the indices
        merged_depth_list.extend(depth_data[top_index:bottom_index+1])
        merged_dt_list.extend(dt_data[top_index:bottom_index+1])
        merged_dts_list.extend(dts_data[top_index:bottom_index+1])
        merged_rhob_list.extend(rhob_data[top_index:bottom_index+1])
        merged_target_list.extend(target_data[top_index:bottom_index+1])
        processed_wells_for_merge += 1

    if processed_wells_for_merge == 0 or not merged_depth_list: # Check if any data was actually merged
        print("No data could be merged for merged analysis (e.g. all wells missed required logs). Cannot proceed with merged optimum angle calculation.")
        return [] # Return empty list for all_wells_data

    merged_depth = np.array(merged_depth_list)
    merged_dt = np.array(merged_dt_list)
    merged_dts = np.array(merged_dts_list)
    merged_rhob = np.array(merged_rhob_list)
    merged_target = np.array(merged_target_list)


    # Convert slowness to velocity (assuming microseconds/ft to m/s conversion)
    pvel = 304800 / merged_dt  # Convert microseconds/ft to m/s
    svel = 304800 / merged_dts  # Convert microseconds/ft to m/s

    # Determine k value based on method selected
    if k_method == 1:
        k = np.nanmean((svel/pvel)**2)
        print(f"Calculated k value from logs (merged): {k:.4f}")
    else:
        k = k_value
        print(f"Using constant k value (merged): {k:.4f}")
    if np.isnan(k):
        print("Warning: Merged k value is NaN. This might affect EEI calculations.")

    angles = range(-90, 91)
    correlations = []

    for angle in angles:
        eei, _, _ = eeimpcalc(pvel, svel, merged_rhob, angle, k, calcmethod=calcmethod)
        correlation = nanaware_corrcoef(eei, merged_target)
        correlations.append(correlation)

    if np.all(np.isnan(correlations)):
        print("All correlations for merged data are NaN. Unable to find optimum angle for merged wells.")
        optimum_angle_merged = 0 # Default or placeholder
        max_correlation_merged = np.nan
    else:
        optimum_angle_merged = angles[np.nanargmax(correlations)]
        max_correlation_merged = np.nanmax(correlations)

    print(f"Optimum angle for Merged Wells: {optimum_angle_merged}°")
    print(f"Maximum correlation coefficient with {target_log_generic_name}: {max_correlation_merged:.4f}")

    # Plot EEI-Target Log Correlation vs Angle
    plt.figure(figsize=(12, 6))
    plt.plot(angles, correlations)
    plt.axvline(x=optimum_angle_merged, color='r', linestyle='--', label=f'Optimum Angle: {optimum_angle_merged}°')
    plt.xlabel('Angle (degrees)')
    plt.ylabel('Correlation Coefficient')
    plt.title(f'EEI-{target_log_generic_name} Correlation vs Angle for Merged Wells')
    plt.legend()
    plt.grid(True)
    plt.show()

    # Calculate EEI using the (merged) optimum angle for each well
    all_wells_data_output = []
    for las in las_files: # Iterate through original las_files to generate output for each
        well_name = las.well.WELL.value
        top_depth, bottom_depth = depth_ranges[well_name]

        current_well_cols_for_eei = find_default_columns(las, log_keywords_for_finding_cols)

        base_mnemonics_for_eei = {}
        missing_eei_calc_logs = []
        for generic_name in required_base_generic_logs:
            mnemonic = current_well_cols_for_eei.get(generic_name)
            if mnemonic is None or mnemonic not in las.curves:
                missing_eei_calc_logs.append(f"{generic_name} (searched: {log_keywords_for_finding_cols.get(generic_name, [])})")
            else:
                base_mnemonics_for_eei[generic_name] = mnemonic

        target_actual_mnemonic_for_eei = current_well_cols_for_eei.get(target_log_generic_name)
        if target_actual_mnemonic_for_eei is None and target_log_generic_name in las.curves:
            target_actual_mnemonic_for_eei = target_log_generic_name
            print(f"Info: Target log '{target_log_generic_name}' used directly for EEI calc in well {well_name}.")


        if target_actual_mnemonic_for_eei is None or target_actual_mnemonic_for_eei not in las.curves:
            missing_eei_calc_logs.append(f"{target_log_generic_name} (resolved mnemonic '{target_actual_mnemonic_for_eei}', searched: {log_keywords_for_finding_cols.get(target_log_generic_name, [target_log_generic_name])})")

        vcl_actual_mnemonic_for_eei = current_well_cols_for_eei.get('VCL') # Optional

        if missing_eei_calc_logs:
            print(f"Warning: For merged EEI calculation plotting, well {well_name} is missing logs: {', '.join(missing_eei_calc_logs)}. Appending placeholder data for this well.")
            all_wells_data_output.append({
                'well_name': well_name, 'depth': None, 'target': None,
                'normalized_eei': None, 'angle': optimum_angle_merged, 'vol_wetclay': None
            })
            continue

        depth_res, target_res, norm_eei_res, vcl_res = calculate_eei(
            las,
            base_mnemonics_for_eei,
            target_actual_mnemonic_for_eei,
            vcl_actual_mnemonic_for_eei,
            top_depth, bottom_depth,
            optimum_angle_merged, # Use the optimum_angle derived from merged data
            calcmethod,
            k_method,
            k_value
        )

        all_wells_data_output.append({
            'well_name': well_name,
            'depth': depth_res,
            'target': target_res,
            'normalized_eei': norm_eei_res,
            'angle': optimum_angle_merged, # Store the angle used
            'vol_wetclay': vcl_res
        })

    return all_wells_data_output

def load_boundaries_from_excel(title="Select Excel file with boundary information"):
    """
    Load boundary information from an Excel file.

    Args:
        title: The title to display in the file dialog

    Returns:
        DataFrame containing well names, surface names, and measured depths
        or None if no file was selected or an error occurred.
    """
    root = tk.Tk()
    root.withdraw()

    file_path = filedialog.askopenfilename(
        title=title,
        filetypes=[("Excel files", "*.xls;*.xlsx")]
    )

    if not file_path:
        print("No Excel file selected.")
        return None

    try:
        # Load the Excel file
        df = pd.read_excel(file_path)

        # Check if the required columns exist
        required_columns = ['Well', 'Surface', 'MD']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            messagebox.showerror(
                "Missing Columns",
                f"The Excel file is missing the following required columns: {', '.join(missing_columns)}.\n"
                f"Please ensure the file contains columns named: {', '.join(required_columns)}"
            )
            return None

        # Basic validation
        if df.empty:
            messagebox.showerror("Empty File", "The Excel file contains no data.")
            return None

        print(f"Successfully loaded boundary data from {file_path}")
        print(f"Found {len(df)} boundary entries for {df['Well'].nunique()} wells")
        return df

    except Exception as e:
        messagebox.showerror("Error Loading File", f"An error occurred while loading the Excel file:\n{str(e)}")
        print(f"Error loading Excel file: {str(e)}")
        return None

def filter_excel_data_for_las_wells(df, las_files):
    """
    Filter Excel data to only include boundaries for wells that exist in the loaded LAS files.

    Args:
        df: DataFrame containing well names, surface names, and measured depths
        las_files: List of LAS file objects

    Returns:
        Filtered DataFrame containing only boundaries for wells in the LAS files
    """
    if df is None:
        return None

    # Get the list of well names from the LAS files
    las_well_names = [las.well.WELL.value for las in las_files]

    # Filter the DataFrame to only include rows where the Well column value is in las_well_names
    filtered_df = df[df['Well'].isin(las_well_names)]

    # Check if we have any matching wells
    if filtered_df.empty:
        print("Warning: No matching wells found in Excel file. The Excel file contains wells:",
              ", ".join(df['Well'].unique()))
        print("But the loaded LAS files contain wells:", ", ".join(las_well_names))
        return None

    # Log the filtering results
    original_well_count = df['Well'].nunique()
    filtered_well_count = filtered_df['Well'].nunique()
    print(f"Filtered Excel data from {original_well_count} wells to {filtered_well_count} wells that match loaded LAS files")
    print(f"Retained wells: {', '.join(filtered_df['Well'].unique())}")

    return filtered_df

def load_excel_depth_ranges(las_files):
    """
    Prompt the user to load an Excel file with depth ranges at the beginning of the program.
    Filter the data to only include boundaries for wells that exist in the loaded LAS files.

    Args:
        las_files: List of LAS file objects

    Returns:
        DataFrame containing well names, surface names, and measured depths (filtered for LAS wells)
        or None if no file was selected, the user canceled, or an error occurred.
    """
    # Ask user if they want to load an Excel file with depth ranges
    root = tk.Tk()
    root.withdraw()

    load_excel = messagebox.askyesno(
        "Load Depth Ranges Excel",
        "Would you like to load an Excel file containing depth ranges now?\n\n"
        "The file should have columns named 'Well', 'Surface', and 'MD'."
    )

    if not load_excel:
        print("User chose not to load Excel file with depth ranges at this time.")
        return None

    # Load the Excel file
    df = load_boundaries_from_excel("Select Excel file with depth ranges")

    # Filter the data to only include boundaries for wells in the LAS files
    return filter_excel_data_for_las_wells(df, las_files)

def select_boundaries_for_all_wells(df, las_well_names):
    """
    Create a dialog to select top and bottom boundaries for all wells at once.

    Args:
        df: DataFrame containing boundary data
        las_well_names: List of well names from LAS files

    Returns:
        Dictionary mapping well names to (top_depth, bottom_depth) tuples,
        or None if cancelled
    """
    # Filter data for wells that exist in both the Excel file and LAS files
    available_wells = [well for well in las_well_names if well in df['Well'].unique()]

    if not available_wells:
        messagebox.showerror(
            "No Matching Wells",
            "No wells in the Excel file match the loaded LAS files."
        )
        return None

    # Create a dictionary to store well data
    well_data_dict = {}
    for well in available_wells:
        # Filter and sort data for this well
        well_data = df[df['Well'] == well].sort_values('MD')
        if not well_data.empty:
            well_data_dict[well] = well_data

    # Create dialog
    dialog = tk.Toplevel()
    dialog.title("Select Boundaries for All Wells")
    dialog.geometry("800x600")  # Larger dialog for the table

    # Create main frame
    main_frame = ttk.Frame(dialog, padding="10")
    main_frame.pack(fill=tk.BOTH, expand=True)

    # Add instructions
    instructions = ttk.Label(
        main_frame,
        text="Select top and bottom boundaries for all wells. The boundaries will be used to define the depth range for analysis.",
        wraplength=780
    )
    instructions.pack(pady=(0, 10))

    # Create a frame for the table
    table_frame = ttk.Frame(main_frame)
    table_frame.pack(fill=tk.BOTH, expand=True, pady=10)

    # Create a canvas with scrollbar for the table
    canvas = tk.Canvas(table_frame)
    scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=canvas.yview)
    scrollable_frame = ttk.Frame(canvas)

    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )

    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)

    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")

    # Create table headers
    ttk.Label(scrollable_frame, text="Well Name", font=("", 10, "bold")).grid(row=0, column=0, padx=10, pady=5, sticky=tk.W)
    ttk.Label(scrollable_frame, text="Top Boundary", font=("", 10, "bold")).grid(row=0, column=1, padx=10, pady=5, sticky=tk.W)
    ttk.Label(scrollable_frame, text="Top Depth", font=("", 10, "bold")).grid(row=0, column=2, padx=10, pady=5, sticky=tk.W)
    ttk.Label(scrollable_frame, text="Bottom Boundary", font=("", 10, "bold")).grid(row=0, column=3, padx=10, pady=5, sticky=tk.W)
    ttk.Label(scrollable_frame, text="Bottom Depth", font=("", 10, "bold")).grid(row=0, column=4, padx=10, pady=5, sticky=tk.W)

    # Dictionary to store selections for each well
    selections = {}

    # Function to update depth labels when surface selection changes
    def update_depth_label(well_name, surface_type):
        surface = selections[well_name][f"{surface_type}_surface"].get()
        well_df = well_data_dict[well_name]

        md_values = well_df[well_df['Surface'] == surface]['MD'].values
        if len(md_values) > 0:
            depth = float(md_values[0])
            selections[well_name][f"{surface_type}_depth_var"].set(f"{depth:.2f}")
            selections[well_name][f"{surface_type}_depth"] = depth
        else:
            selections[well_name][f"{surface_type}_depth_var"].set("N/A")
            selections[well_name][f"{surface_type}_depth"] = None

    # Create rows for each well
    row = 1
    for well_name in available_wells:
        if well_name in well_data_dict:
            well_df = well_data_dict[well_name]
            surfaces = well_df['Surface'].unique().tolist()

            if len(surfaces) < 2:
                # Skip wells with fewer than 2 surfaces (need at least top and bottom)
                continue

            # Create a dictionary to store selections for this well
            selections[well_name] = {
                "top_surface": tk.StringVar(),
                "bottom_surface": tk.StringVar(),
                "top_depth_var": tk.StringVar(),
                "bottom_depth_var": tk.StringVar(),
                "top_depth": None,
                "bottom_depth": None
            }

            # Set default values
            selections[well_name]["top_surface"].set(surfaces[0])
            selections[well_name]["bottom_surface"].set(surfaces[-1])

            # Create widgets for this well
            ttk.Label(scrollable_frame, text=well_name).grid(row=row, column=0, padx=10, pady=5, sticky=tk.W)

            # Top boundary combobox
            top_combo = ttk.Combobox(
                scrollable_frame,
                textvariable=selections[well_name]["top_surface"],
                values=surfaces,
                state="readonly",
                width=20
            )
            top_combo.grid(row=row, column=1, padx=10, pady=5, sticky=tk.W)

            # Top depth label
            top_depth_label = ttk.Label(
                scrollable_frame,
                textvariable=selections[well_name]["top_depth_var"]
            )
            top_depth_label.grid(row=row, column=2, padx=10, pady=5, sticky=tk.W)

            # Bottom boundary combobox
            bottom_combo = ttk.Combobox(
                scrollable_frame,
                textvariable=selections[well_name]["bottom_surface"],
                values=surfaces,
                state="readonly",
                width=20
            )
            bottom_combo.grid(row=row, column=3, padx=10, pady=5, sticky=tk.W)

            # Bottom depth label
            bottom_depth_label = ttk.Label(
                scrollable_frame,
                textvariable=selections[well_name]["bottom_depth_var"]
            )
            bottom_depth_label.grid(row=row, column=4, padx=10, pady=5, sticky=tk.W)

            # Set up callbacks for comboboxes
            selections[well_name]["top_surface"].trace_add(
                "write",
                lambda *args, w=well_name: update_depth_label(w, "top")
            )
            selections[well_name]["bottom_surface"].trace_add(
                "write",
                lambda *args, w=well_name: update_depth_label(w, "bottom")
            )

            # Initialize depth labels
            update_depth_label(well_name, "top")
            update_depth_label(well_name, "bottom")

            row += 1

    # Create buttons frame
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(pady=10)

    # Result variable
    result = {"boundaries": {}, "cancelled": False}

    def on_ok():
        # Show a progress indicator
        status_label = ttk.Label(main_frame, text="Processing selections...", font=("", 10, "italic"))
        status_label.pack(before=button_frame, pady=5)
        dialog.update_idletasks()  # Force UI update to show the status label

        # Validate selections
        invalid_wells = []
        for well_name, selection in selections.items():
            top_depth = selection["top_depth"]
            bottom_depth = selection["bottom_depth"]

            if top_depth is None or bottom_depth is None:
                invalid_wells.append(well_name)
            else:
                # Store the boundaries
                result["boundaries"][well_name] = (top_depth, bottom_depth)

        if invalid_wells:
            status_label.destroy()  # Remove the status label
            messagebox.showerror(
                "Invalid Selections",
                f"Could not retrieve valid depth values for the following wells: {', '.join(invalid_wells)}"
            )
            return

        # Update status to indicate completion
        status_label.config(text="Selections complete! Closing dialog...")
        dialog.update_idletasks()  # Force UI update

        # Use after() to ensure the UI updates before destroying the dialog
        dialog.after(500, lambda: dialog.destroy())
        result["cancelled"] = False

    def on_cancel():
        result["cancelled"] = True
        dialog.destroy()

    # Create buttons
    ttk.Button(button_frame, text="OK", command=on_ok).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="Cancel", command=on_cancel).pack(side=tk.LEFT, padx=5)

    # Run dialog
    dialog.protocol("WM_DELETE_WINDOW", on_cancel)
    dialog.transient()
    dialog.grab_set()

    # Make the dialog modal and wait for it to be destroyed
    dialog.wait_visibility()
    dialog.focus_set()
    dialog.wait_window()

    if result["cancelled"]:
        print("Batch selection was cancelled by the user.")
        return None

    print(f"Batch selection completed successfully for {len(result['boundaries'])} wells.")
    return result["boundaries"]

def select_boundaries_from_excel(df, well_name):
    """
    Create a dialog to select top and bottom boundaries from Excel data for a specific well.
    The dialog will only show boundaries for the specified well.

    Args:
        df: DataFrame containing boundary data
        well_name: Name of the well to filter data for

    Returns:
        Tuple of (top_depth, bottom_depth) or None if cancelled
    """
    # Filter data for the current well
    well_data = df[df['Well'] == well_name]

    if well_data.empty:
        messagebox.showerror(
            "Missing Well Data",
            f"No boundary data found for well '{well_name}' in the Excel file."
        )
        return None

    # Sort the data by depth to make selection more intuitive
    well_data = well_data.sort_values('MD')

    # Create dialog
    dialog = tk.Toplevel()
    dialog.title(f"Select Boundaries for {well_name}")
    dialog.geometry("400x300")

    # Create frame
    frame = ttk.Frame(dialog, padding="10")
    frame.pack(fill=tk.BOTH, expand=True)

    # Get unique surface names for this well
    surfaces = well_data['Surface'].unique().tolist()

    # Create variables to store selections
    top_surface_var = tk.StringVar()
    bottom_surface_var = tk.StringVar()

    # Set default values if available
    if len(surfaces) > 0:
        top_surface_var.set(surfaces[0])
    if len(surfaces) > 1:
        bottom_surface_var.set(surfaces[-1])

    # Function to update depth labels when surface selection changes
    def update_depth_labels(*args):
        top_surface = top_surface_var.get()
        bottom_surface = bottom_surface_var.get()

        top_md = well_data[well_data['Surface'] == top_surface]['MD'].values
        bottom_md = well_data[well_data['Surface'] == bottom_surface]['MD'].values

        if len(top_md) > 0:
            top_depth_label.config(text=f"Depth: {top_md[0]:.2f}")
        else:
            top_depth_label.config(text="Depth: N/A")

        if len(bottom_md) > 0:
            bottom_depth_label.config(text=f"Depth: {bottom_md[0]:.2f}")
        else:
            bottom_depth_label.config(text="Depth: N/A")

    # Create widgets
    ttk.Label(frame, text="Select Top Boundary:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
    top_combo = ttk.Combobox(frame, textvariable=top_surface_var, values=surfaces, state="readonly")
    top_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
    top_depth_label = ttk.Label(frame, text="Depth: ")
    top_depth_label.grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))

    ttk.Label(frame, text="Select Bottom Boundary:").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
    bottom_combo = ttk.Combobox(frame, textvariable=bottom_surface_var, values=surfaces, state="readonly")
    bottom_combo.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
    bottom_depth_label = ttk.Label(frame, text="Depth: ")
    bottom_depth_label.grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))

    # Register callbacks
    top_surface_var.trace_add("write", update_depth_labels)
    bottom_surface_var.trace_add("write", update_depth_labels)

    # Initialize depth labels
    update_depth_labels()

    # Result variable
    result = {"top_depth": None, "bottom_depth": None, "cancelled": False}

    def on_ok():
        # Show processing indicator
        status_label = ttk.Label(frame, text="Processing...", font=("", 9, "italic"))
        status_label.grid(row=5, column=0, columnspan=2, pady=(5, 0))
        dialog.update_idletasks()  # Force UI update

        top_surface = top_surface_var.get()
        bottom_surface = bottom_surface_var.get()

        top_md = well_data[well_data['Surface'] == top_surface]['MD'].values
        bottom_md = well_data[well_data['Surface'] == bottom_surface]['MD'].values

        if len(top_md) == 0 or len(bottom_md) == 0:
            status_label.destroy()
            messagebox.showerror("Missing Data", "Could not retrieve depth values for selected surfaces.")
            return

        result["top_depth"] = float(top_md[0])
        result["bottom_depth"] = float(bottom_md[0])

        # Update status and close dialog
        status_label.config(text="Selection complete! Closing...")
        dialog.update_idletasks()  # Force UI update

        # Use after() to ensure the UI updates before destroying the dialog
        dialog.after(300, lambda: dialog.destroy())

    def on_cancel():
        result["cancelled"] = True
        dialog.destroy()

    # Create buttons
    button_frame = ttk.Frame(frame)
    button_frame.grid(row=4, column=0, columnspan=2, pady=(10, 0))

    ttk.Button(button_frame, text="OK", command=on_ok).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="Cancel", command=on_cancel).pack(side=tk.LEFT, padx=5)

    # Configure grid weights
    frame.columnconfigure(1, weight=1)

    # Run dialog
    dialog.protocol("WM_DELETE_WINDOW", on_cancel)
    dialog.transient()
    dialog.grab_set()

    # Make the dialog modal and wait for it to be destroyed
    dialog.wait_visibility()
    dialog.focus_set()
    dialog.wait_window()

    if result["cancelled"]:
        print(f"Selection cancelled for well {well_name}.")
        return None

    print(f"Selection completed for well {well_name}: ({result['top_depth']:.2f}, {result['bottom_depth']:.2f})")
    return (result["top_depth"], result["bottom_depth"])

def get_depth_ranges(las_files, log_keywords_for_finding_cols, preloaded_excel_df=None):
    """
    Create a GUI to display well names and allow users to input top and bottom depths for analysis.
    Provides two methods:
    1. Manual input: Directly enter depth values
    2. Excel file import: Select depths from geological markers in an Excel file

    Args:
        las_files: List of LAS file objects
        log_keywords_for_finding_cols: Dictionary mapping generic names to possible mnemonics
        preloaded_excel_df: Optional pre-loaded Excel DataFrame with depth ranges

    If no input is provided, use the min and max non-NaN values from the DT log, if available.
    """
    # Calculate default depth values for each well
    default_depth_values_for_fallback = {}

    for las in las_files:
        well_name = las.well.WELL.value

        current_well_cols_for_depth = find_default_columns(las, log_keywords_for_finding_cols)
        depth_mnemonic = current_well_cols_for_depth.get('DEPTH')
        dt_mnemonic = current_well_cols_for_depth.get('DT')

        default_top_depth_val = 0.0  # Fallback default
        default_bottom_depth_val = 0.0 # Fallback default

        if depth_mnemonic and depth_mnemonic in las.curves:
            depth_data_for_range = np.array(las[depth_mnemonic].data)

            if dt_mnemonic and dt_mnemonic in las.curves:
                dt_data_for_range = np.array(las[dt_mnemonic].data)
                # Consider only depths where DT is valid
                valid_dt_mask = np.isfinite(dt_data_for_range) & np.isfinite(depth_data_for_range)
                valid_depths_for_dt_range = depth_data_for_range[valid_dt_mask]

                if valid_depths_for_dt_range.size > 0:
                    default_top_depth_val = np.min(valid_depths_for_dt_range)
                    default_bottom_depth_val = np.max(valid_depths_for_dt_range)
                elif np.any(np.isfinite(depth_data_for_range)): # Fallback to full depth range if DT is all NaN
                    default_top_depth_val = np.nanmin(depth_data_for_range)
                    default_bottom_depth_val = np.nanmax(depth_data_for_range)
                    print(f"Info: DT log '{dt_mnemonic}' for well {well_name} has no valid data points within depth range. Using full depth range for defaults.")
                else: # Depth log itself is all NaN or empty
                    print(f"Warning: Depth log '{depth_mnemonic}' for well {well_name} is empty or all NaN. Using 0,0 for default depth range.")
            elif np.any(np.isfinite(depth_data_for_range)): # DT log not found, use full depth range
                default_top_depth_val = np.nanmin(depth_data_for_range)
                default_bottom_depth_val = np.nanmax(depth_data_for_range)
                print(f"Info: DT log not found for well {well_name} (searched for '{dt_mnemonic}'). Using full depth range for defaults.")
            else: # Depth log is all NaN or empty, and DT log not found
                 print(f"Warning: Depth log '{depth_mnemonic}' for well {well_name} is empty or all NaN, and DT log not found. Using 0,0 for default depth range.")
        else:
            print(f"Error: DEPTH log not found for well {well_name} (searched for '{depth_mnemonic}'). Cannot set default depth range. Using 0,0.")

        default_depth_values_for_fallback[well_name] = (default_top_depth_val, default_bottom_depth_val)

    # Create main dialog for method selection
    root = tk.Tk()
    root.title("Set Depth Ranges for Wells")
    root.geometry("600x500")

    # Create a frame
    main_frame = ttk.Frame(root, padding="10")
    main_frame.pack(fill=tk.BOTH, expand=True)

    # Method selection
    method_frame = ttk.LabelFrame(main_frame, text="Select Method for Defining Boundaries", padding="10")
    method_frame.pack(fill=tk.X, padx=5, pady=5)

    # Set default method - use "excel" if preloaded Excel data is available
    default_method = "excel" if preloaded_excel_df is not None else "manual"
    method_var = tk.StringVar(value=default_method)

    # Variable to store the submit button reference
    submit_btn_ref = {"btn": None}

    # Define a command to call when radio buttons are clicked
    def on_radio_click():
        method = method_var.get()
        if method == "manual":
            create_manual_ui()
            # Re-enable the Submit button for manual input
            if submit_btn_ref["btn"] is not None:
                submit_btn_ref["btn"].config(state=tk.NORMAL)
        else:  # excel
            create_excel_ui()
            # Disable the Submit button until a valid Excel file is loaded
            if submit_btn_ref["btn"] is not None:
                submit_btn_ref["btn"].config(state=tk.DISABLED)

    ttk.Radiobutton(
        method_frame,
        text="Manual Input",
        variable=method_var,
        value="manual",
        command=on_radio_click
    ).pack(anchor=tk.W, pady=2)

    ttk.Radiobutton(
        method_frame,
        text="Import from Excel File",
        variable=method_var,
        value="excel",
        command=on_radio_click
    ).pack(anchor=tk.W, pady=2)

    # Content frame for the selected method
    content_frame = ttk.Frame(main_frame, padding="10")
    content_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    # Variables to store entries for manual method
    manual_entries = []

    # Variable to store Excel data
    excel_data = {"df": preloaded_excel_df}

    # Function to create manual input UI
    def create_manual_ui():
        # Clear the content frame
        for widget in content_frame.winfo_children():
            widget.destroy()

        # Create scrollable frame for many wells
        canvas = tk.Canvas(content_frame)
        scrollbar = ttk.Scrollbar(content_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Create and set column headers
        ttk.Label(scrollable_frame, text="Well Name").grid(row=0, column=0, padx=5, pady=5)
        ttk.Label(scrollable_frame, text="Top Depth").grid(row=0, column=1, padx=5, pady=5)
        ttk.Label(scrollable_frame, text="Bottom Depth").grid(row=0, column=2, padx=5, pady=5)

        # Clear previous entries
        manual_entries.clear()

        # Create entries for each well
        for i, las in enumerate(las_files, start=1):
            well_name = las.well.WELL.value
            default_top, default_bottom = default_depth_values_for_fallback[well_name]

            ttk.Label(scrollable_frame, text=well_name).grid(row=i, column=0, padx=5, pady=2)
            top_entry = ttk.Entry(scrollable_frame)
            top_entry.insert(0, str(default_top))
            top_entry.grid(row=i, column=1, padx=5, pady=2)
            bottom_entry = ttk.Entry(scrollable_frame)
            bottom_entry.insert(0, str(default_bottom))
            bottom_entry.grid(row=i, column=2, padx=5, pady=2)

            manual_entries.append((well_name, top_entry, bottom_entry))

    # Function to create Excel import UI
    def create_excel_ui():
        # Clear the content frame
        for widget in content_frame.winfo_children():
            widget.destroy()

        # Declare submit_btn as global so we can access it
        global submit_btn

        # Create UI for Excel import
        excel_frame = ttk.Frame(content_frame)
        excel_frame.pack(fill=tk.BOTH, expand=True)

        # Status label - show different message if Excel data is already loaded
        initial_status = "No Excel file loaded. Click 'Load Excel File' to import boundary data."
        load_button_text = "Load Excel File"

        if excel_data["df"] is not None:
            df = excel_data["df"]
            initial_status = f"Excel file already loaded and ready to use.\nFound {len(df)} boundary entries for {df['Well'].nunique()} wells.\nClick 'Submit' to proceed with selecting boundaries for each well."
            # Enable the Submit button since we already have Excel data
            if submit_btn_ref["btn"] is not None:
                submit_btn_ref["btn"].config(state=tk.NORMAL)
            # Change the button text to indicate loading a different file
            load_button_text = "Load Different Excel File"

        status_var = tk.StringVar(value=initial_status)
        status_label = ttk.Label(excel_frame, textvariable=status_var, wraplength=500)
        status_label.pack(pady=10)

        # Function to load Excel file
        def on_load_excel():
            # Update the button text to indicate loading is in progress
            load_button.config(text="Loading...", state=tk.DISABLED)

            # Update the UI to show loading is in progress
            root.update_idletasks()

            df = load_boundaries_from_excel()
            if df is not None:
                # Filter the Excel data to only include wells that match the loaded LAS files
                filtered_df = filter_excel_data_for_las_wells(df, las_files)

                if filtered_df is not None:
                    # Check if this is a different file than what was already loaded
                    is_different = True
                    if excel_data["df"] is not None:
                        # Simple check - compare number of rows and wells
                        old_rows = len(excel_data["df"])
                        old_wells = excel_data["df"]['Well'].nunique()
                        new_rows = len(filtered_df)
                        new_wells = filtered_df['Well'].nunique()

                        if old_rows == new_rows and old_wells == new_wells:
                            is_different = False

                    excel_data["df"] = filtered_df

                    if is_different:
                        status_var.set(f"New Excel file loaded and filtered successfully.\n"
                                      f"Found {len(filtered_df)} boundary entries for {filtered_df['Well'].nunique()} wells that match loaded LAS files.\n"
                                      f"Click 'Submit' to proceed with selecting boundaries for each well.")
                    else:
                        status_var.set(f"Excel file loaded (appears to be the same or similar to previous file).\n"
                                      f"Found {len(filtered_df)} boundary entries for {filtered_df['Well'].nunique()} wells that match loaded LAS files.\n"
                                      f"Click 'Submit' to proceed with selecting boundaries for each well.")

                    # Preview data
                    preview_text.delete(1.0, tk.END)
                    preview_text.insert(tk.END, filtered_df.head(10).to_string())
                    # Add a note about the data being filtered for LAS wells
                    preview_text.insert(tk.END, "\n\nNote: This data is filtered to show only wells that match your loaded LAS files.")

                    # Enable the Submit button now that a valid Excel file is loaded
                    if submit_btn_ref["btn"] is not None:
                        submit_btn_ref["btn"].config(state=tk.NORMAL)

                    # Update the button text to indicate loading a different file
                    load_button.config(text="Load Different Excel File", state=tk.NORMAL)
                else:
                    status_var.set("No matching wells found in Excel file. Please load a file with wells that match the loaded LAS files.")
                    # Keep the Submit button disabled since no matching wells were found
                    if submit_btn_ref["btn"] is not None:
                        submit_btn_ref["btn"].config(state=tk.DISABLED)
                    # Reset the button text
                    load_button.config(text="Load Excel File", state=tk.NORMAL)
            else:
                status_var.set("Failed to load Excel file. Please try again.")
                # Keep the Submit button disabled on failure
                if submit_btn_ref["btn"] is not None:
                    submit_btn_ref["btn"].config(state=tk.DISABLED)
                # Reset the button text
                load_button.config(text="Load Excel File", state=tk.NORMAL)

        # Create a frame for the buttons
        buttons_frame = ttk.Frame(excel_frame)
        buttons_frame.pack(pady=5)

        # Load button - use the text based on whether Excel data is already loaded
        load_button = ttk.Button(buttons_frame, text=load_button_text, command=on_load_excel)
        load_button.pack(side=tk.LEFT, padx=5)

        # Add a button to preview boundaries for all wells
        def on_preview_boundaries():
            if excel_data["df"] is None:
                messagebox.showerror("No Data", "Please load an Excel file first.")
                return

            # Get the list of well names from the LAS files
            las_well_names = [las.well.WELL.value for las in las_files]

            # Show a preview of the boundaries that would be selected
            preview_text.delete(1.0, tk.END)
            preview_text.insert(tk.END, "Preview of available boundaries for each well:\n\n")

            # Get unique wells in Excel that also exist in LAS files
            df_excel = excel_data["df"]
            matching_wells = [well for well in las_well_names if well in df_excel['Well'].unique()]

            if not matching_wells:
                preview_text.insert(tk.END, "No matching wells found in Excel file.")
                return

            # Show a preview for each well
            for well_name in matching_wells:
                well_data = df_excel[df_excel['Well'] == well_name].sort_values('MD')
                if not well_data.empty:
                    surfaces = well_data['Surface'].unique().tolist()
                    if len(surfaces) >= 2:
                        top_surface = surfaces[0]
                        bottom_surface = surfaces[-1]
                        top_md = well_data[well_data['Surface'] == top_surface]['MD'].values[0]
                        bottom_md = well_data[well_data['Surface'] == bottom_surface]['MD'].values[0]
                        preview_text.insert(tk.END, f"{well_name}:\n")
                        preview_text.insert(tk.END, f"  Top: {top_surface} ({top_md:.2f})\n")
                        preview_text.insert(tk.END, f"  Bottom: {bottom_surface} ({bottom_md:.2f})\n\n")
                    else:
                        preview_text.insert(tk.END, f"{well_name}: Insufficient boundaries (need at least 2)\n\n")
                else:
                    preview_text.insert(tk.END, f"{well_name}: No data found\n\n")

            preview_text.insert(tk.END, "\nNote: These are the default boundaries that will be used. You can change them in the batch selection dialog.")

        preview_button = ttk.Button(buttons_frame, text="Preview Boundaries", command=on_preview_boundaries)
        preview_button.pack(side=tk.LEFT, padx=5)

        # Preview frame
        preview_frame = ttk.LabelFrame(excel_frame, text="Data Preview (first 10 rows)")
        preview_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        preview_text = tk.Text(preview_frame, height=10, wrap=tk.NONE)
        preview_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        preview_scrollbar = ttk.Scrollbar(preview_frame, orient="vertical", command=preview_text.yview)
        preview_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        preview_text.configure(yscrollcommand=preview_scrollbar.set)

        # Horizontal scrollbar for preview
        h_scrollbar = ttk.Scrollbar(preview_frame, orient="horizontal", command=preview_text.xview)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        preview_text.configure(xscrollcommand=h_scrollbar.set)

        # Show preview of preloaded Excel data if available
        if excel_data["df"] is not None:
            preview_text.delete(1.0, tk.END)
            preview_text.insert(tk.END, excel_data["df"].head(10).to_string())
            # Add a note about the data being already filtered for LAS wells
            preview_text.insert(tk.END, "\n\nNote: This data is already filtered to show only wells that match your loaded LAS files.")

    # Note: Method switching is now handled directly by the radio button command (on_radio_click)

    # Initialize UI based on the selected method
    on_radio_click()

    # Result variable
    depth_ranges_output = {}

    def on_submit():
        method = method_var.get()
        processed_excel_wells = set() # Keep track of wells processed from Excel

        if method == "manual":
            # Process manual entries
            for well_name, top_entry, bottom_entry in manual_entries:
                try:
                    top_depth_val = float(top_entry.get())
                    bottom_depth_val = float(bottom_entry.get())
                    depth_ranges_output[well_name] = (top_depth_val, bottom_depth_val)
                except ValueError:
                    # If the user input is invalid, fallback to determined default non-NaN DT range
                    print(f"Invalid depth input for {well_name}, using determined default range: {default_depth_values_for_fallback[well_name]}.")
                    depth_ranges_output[well_name] = default_depth_values_for_fallback[well_name]
            root.quit()

        else:  # excel
            if excel_data["df"] is None:
                messagebox.showerror("No Data", "Please load an Excel file first.")
                return

            df_excel = excel_data["df"]

            # Get the list of well names from the LAS files
            las_well_names = [las.well.WELL.value for las in las_files]

            # Update status to indicate batch selection is starting
            status_var = tk.StringVar(value="Starting batch selection dialog...")
            status_label = ttk.Label(main_frame, textvariable=status_var, font=("", 10, "italic"))
            status_label.pack(before=submit_btn_ref["btn"], pady=5)
            root.update_idletasks()  # Force UI update

            # Show a dialog to select boundaries for all wells at once
            print("Opening batch selection dialog for all wells...")
            all_boundaries = select_boundaries_for_all_wells(df_excel, las_well_names)

            # Update status based on the result
            if all_boundaries:
                status_var.set("Processing selected boundaries...")
                root.update_idletasks()  # Force UI update

                # Apply the selected boundaries
                for well_name, boundaries in all_boundaries.items():
                    depth_ranges_output[well_name] = boundaries
                    processed_excel_wells.add(well_name)
                    print(f"Boundaries set for {well_name} from Excel batch selection: {boundaries}")

                # Apply default values for any LAS wells that weren't in the Excel file or weren't selected
                for las_well_name in las_well_names:
                    if las_well_name not in depth_ranges_output:
                        depth_ranges_output[las_well_name] = default_depth_values_for_fallback[las_well_name]
                        print(f"No boundaries selected for {las_well_name}. Applied LAS-derived default range: {default_depth_values_for_fallback[las_well_name]}")

                status_var.set("Boundary selection completed successfully!")
            else:
                # User cancelled the batch selection dialog
                status_var.set("Batch selection cancelled. Applying default depth ranges...")
                root.update_idletasks()  # Force UI update

                print("Batch selection cancelled. Applying default depth ranges for all wells.")
                for las_well_name in las_well_names:
                    depth_ranges_output[las_well_name] = default_depth_values_for_fallback[las_well_name]
                    print(f"Applied default depth range for {las_well_name}: {default_depth_values_for_fallback[las_well_name]}")

                status_var.set("Default depth ranges applied to all wells.")

            # Final update before closing
            root.update_idletasks()  # Force UI update

            # Use after() to ensure the UI updates before closing
            root.after(1000, root.quit)

    # Submit button - instantiated as a variable so it can be enabled/disabled dynamically
    submit_btn = ttk.Button(main_frame, text="Submit", command=on_submit)
    submit_btn.pack(pady=10)
    # Store the reference to the submit button
    submit_btn_ref["btn"] = submit_btn

    # Set initial state based on the selected method
    if method_var.get() == "manual":
        submit_btn.config(state=tk.NORMAL)
    else:  # excel
        # For Excel method, enable only if we have preloaded data
        if excel_data["df"] is not None:
            submit_btn.config(state=tk.NORMAL)
        else:
            submit_btn.config(state=tk.DISABLED)


    # Run the dialog
    root.mainloop()
    root.destroy()

    return depth_ranges_output

def get_target_log(available_logs, common_actual_mnemonics, common_generic_keywords):
    """
    Create a scrollable list for target log selection, highlighting logs present in all wells.
    """
    root = tk.Tk()
    root.title("Select Target Log")
    root.geometry("300x400")  # Increased window size

    frame = ttk.Frame(root, padding="10")
    frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
    root.columnconfigure(0, weight=1)
    root.rowconfigure(0, weight=1)

    label = ttk.Label(frame, text="Select the log to correlate with EEI:")
    label.grid(row=0, column=0, pady=(0, 5))

    # Add explanatory label for highlighted logs
    info_label = ttk.Label(frame, text="Logs marked with ★ and highlighted in green are present in all LAS files.", font=("", 8, "italic"))
    info_label.grid(row=1, column=0, pady=(0, 10))

    # Create Listbox
    listbox = tk.Listbox(frame, selectmode=tk.SINGLE, exportselection=0)
    listbox.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

    # Add a scrollbar
    scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=listbox.yview)
    scrollbar.grid(row=2, column=1, sticky=(tk.N, tk.S))
    listbox.configure(yscrollcommand=scrollbar.set)

    # Create a mapping between display text and actual log name
    display_to_actual = {}

    # Populate the Listbox and apply highlighting
    for log in available_logs:
        # Check if the log is common (either actual mnemonic or generic keyword)
        is_common = (log.upper() in common_actual_mnemonics) or (log in common_generic_keywords)

        if is_common:
            # Add a star prefix and store in a brighter green color
            display_text = f"★ {log}"
            listbox.insert(tk.END, display_text)
            listbox.itemconfigure(tk.END, foreground='#00CC00')  # Brighter green color
            display_to_actual[display_text] = log
        else:
            # Regular logs without prefix
            listbox.insert(tk.END, log)
            display_to_actual[log] = log

    # Select the first item by default
    if available_logs:
        listbox.selection_set(0)

    selected_log = tk.StringVar()

    def on_select():
        selected_indices = listbox.curselection()
        if selected_indices:
            display_text = listbox.get(selected_indices[0])
            # Convert display text back to actual log name
            actual_log = display_to_actual[display_text]
            selected_log.set(actual_log)
        root.quit()

    select_button = ttk.Button(frame, text="Select", command=on_select)
    select_button.grid(row=3, column=0, pady=(10, 0))

    frame.columnconfigure(0, weight=1)
    frame.rowconfigure(2, weight=1)

    root.mainloop()
    root.destroy()

    return selected_log.get()


def select_alternative_mnemonic(las, missing_target_log, well_name):
    """
    Display a dialog showing all available curve mnemonics in a specific well
    and allow the user to select an alternative mnemonic to use as a substitute
    for the missing target log.

    Args:
        las: The LAS file object
        missing_target_log: The name of the missing target log
        well_name: The name of the well being processed

    Returns:
        The selected alternative mnemonic or None if the user chooses to skip
    """
    root = tk.Tk()
    root.title(f"Select Alternative for '{missing_target_log}' in Well '{well_name}'")
    root.geometry("500x400")  # Larger size for better visibility

    frame = ttk.Frame(root, padding="10")
    frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
    root.columnconfigure(0, weight=1)
    root.rowconfigure(0, weight=1)

    # Create header label with detailed information
    header_text = (f"Target log '{missing_target_log}' is missing in well '{well_name}'.\n"
                  f"Please select an alternative curve to use, or click 'Skip Well' to exclude this well from analysis.")
    header_label = ttk.Label(frame, text=header_text, wraplength=480, justify="center")
    header_label.grid(row=0, column=0, columnspan=2, pady=(0, 15))

    # Create a frame for the listbox and its scrollbar
    list_frame = ttk.Frame(frame)
    list_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
    list_frame.columnconfigure(0, weight=1)
    list_frame.rowconfigure(0, weight=1)

    # Create Listbox with curve information
    columns = ("Mnemonic", "Unit", "Description")
    tree = ttk.Treeview(list_frame, columns=columns, show="headings", selectmode="browse")

    # Set column headings
    for col in columns:
        tree.heading(col, text=col)
        tree.column(col, width=100)

    # Add vertical scrollbar
    vsb = ttk.Scrollbar(list_frame, orient="vertical", command=tree.yview)
    tree.configure(yscrollcommand=vsb.set)

    # Grid the treeview and scrollbar
    tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
    vsb.grid(row=0, column=1, sticky=(tk.N, tk.S))

    # Populate the treeview with curve information
    for curve in las.curves:
        mnemonic = curve.mnemonic
        unit = curve.unit if hasattr(curve, 'unit') else ""
        descr = curve.descr if hasattr(curve, 'descr') else ""
        tree.insert("", "end", values=(mnemonic, unit, descr))

    # Variables to store the result
    result = {"mnemonic": None}

    # Define button actions
    def on_select():
        selection = tree.selection()
        if selection:
            item = tree.item(selection[0])
            result["mnemonic"] = item["values"][0]  # Get the mnemonic
        root.quit()

    def on_skip():
        result["mnemonic"] = None
        root.quit()

    # Create buttons
    button_frame = ttk.Frame(frame)
    button_frame.grid(row=2, column=0, columnspan=2, pady=(15, 0))

    select_button = ttk.Button(button_frame, text="Use Selected Curve", command=on_select)
    select_button.grid(row=0, column=0, padx=5)

    skip_button = ttk.Button(button_frame, text="Skip Well", command=on_skip)
    skip_button.grid(row=0, column=1, padx=5)

    # Configure frame weights
    frame.columnconfigure(0, weight=1)
    frame.rowconfigure(1, weight=1)

    # Start the dialog
    root.mainloop()
    root.destroy()

    return result["mnemonic"]


# MAIN EXECUTION SCRIPT
if __name__ == "__main__":
    # Load LAS files
    las_files = load_multiple_las_files()
    if not las_files:
        print("No LAS files selected. Exiting.")
        exit()

    # Validate essential logs
    validation_results = validate_essential_logs(las_files, log_keywords)
    validation_summary = generate_validation_report(validation_results)

    # Categorize log curves
    log_categories = categorize_log_curves(las_files)
    display_log_inventory(log_categories)

    # Determine which logs are present in all wells
    total_wells = len(las_files)
    common_actual_mnemonics = set()
    common_generic_keywords = set()

    for curve_key, info in log_categories.items():
        if len(info['files']) == total_wells:
            # This curve (by its uppercase mnemonic) is in all files
            common_actual_mnemonics.add(curve_key)

            # Check if this common mnemonic is an alias for any generic keyword
            for generic_keyword, aliases in log_keywords.items():
                if curve_key in [alias.upper() for alias in aliases]:
                    common_generic_keywords.add(generic_keyword)

    print(f"\nFound {len(common_actual_mnemonics)} actual mnemonics present in all {total_wells} wells.")
    print(f"Found {len(common_generic_keywords)} generic keywords whose aliases are present in all {total_wells} wells.")


    # Dictionary to track alternative mnemonics selected by the user
    alternative_mnemonics = {}

    # Ask user if they want to continue despite missing logs
    if validation_summary['invalid_files'] > 0:
        root = tk.Tk()
        root.withdraw()
        continue_analysis = messagebox.askyesno(
            "Missing Essential Logs",
            f"{validation_summary['invalid_files']} out of {validation_summary['total_files']} files are missing essential logs. Continue anyway?"
        )
        if not continue_analysis:
            print("Analysis cancelled by user. Exiting.")
            exit()

    # Calculate additional logs and add them to the LAS files
    for las in las_files:
        log_available_curves(las)  # Log the available curves for each well
        # Use find_default_columns to get specific mnemonics for this well
        current_well_mnemonics = find_default_columns(las, log_keywords)

        # Check for PHIE, SWE, PHIT, SWT for derived logs
        phie_m = current_well_mnemonics.get('PHIE')
        swe_m = current_well_mnemonics.get('SWE')
        phit_m = current_well_mnemonics.get('PHIT')
        swt_m = current_well_mnemonics.get('SWT')

        if all(m and m in las.curves for m in [phie_m, swe_m, phit_m, swt_m]):
            phie_data = np.array(las[phie_m].data)
            swe_data = np.array(las[swe_m].data)
            phit_data = np.array(las[phit_m].data)
            swt_data = np.array(las[swt_m].data)

            las.append_curve('PHIE_1_SWE', phie_data * (1 - swe_data), unit='v/v', descr='PHIE * (1-SWE)')
            las.append_curve('PHIT_1_SWT', phit_data * (1 - swt_data), unit='v/v', descr='PHIT * (1-SWT)')
        else:
            print(f"Info: Skipping PHIE_1_SWE/PHIT_1_SWT calculation for well {las.well.WELL.value} due to missing one or more input logs (PHIE, SWE, PHIT, SWT).")

        # Check for NPHI, SWT for NPHI_SHC
        nphi_m = current_well_mnemonics.get('NPHI')
        # swt_m is already defined from above
        if nphi_m and nphi_m in las.curves and swt_m and swt_m in las.curves: # Ensure swt_m is valid here too
            nphi_data = np.array(las[nphi_m].data)
            swt_data_for_nphi_shc = np.array(las[swt_m].data) # Re-access swt_data for clarity or if scope changes
            nphi_shc_data = nphi_data * (1 - swt_data_for_nphi_shc)
            las.append_curve('NPHI_SHC', nphi_shc_data, unit='v/v', descr='NPHI * (1-SWT)')
        else:
            print(f"Info: Skipping NPHI_SHC calculation for well {las.well.WELL.value} due to missing NPHI or SWT.")

        # Check for KDRY, KSOLID
        kdry_m = current_well_mnemonics.get('KDRY')
        ksolid_m = current_well_mnemonics.get('KSOLID')
        if kdry_m and kdry_m in las.curves and ksolid_m and ksolid_m in las.curves:
            kdry_data = np.array(las[kdry_m].data)
            ksolid_data = np.array(las[ksolid_m].data)
            kdks_data = np.where(ksolid_data != 0, kdry_data / ksolid_data, np.nan)
            las.append_curve('KDKS', kdks_data, unit='', descr='KDRY / KSOLID')
        else:
            print(f"Info: Skipping KDKS calculation for well {las.well.WELL.value} due to missing KDRY or KSOLID.")

        # Check for GDRY, GSOLID
        gdry_m = current_well_mnemonics.get('GDRY')
        gsolid_m = current_well_mnemonics.get('GSOLID')
        if gdry_m and gdry_m in las.curves and gsolid_m and gsolid_m in las.curves:
            gdry_data = np.array(las[gdry_m].data)
            gsolid_data = np.array(las[gsolid_m].data)
            gdgs_data = np.where(gsolid_data != 0, gdry_data / gsolid_data, np.nan)
            las.append_curve('GDGS', gdgs_data, unit='', descr='GDRY / GSOLID')
        else:
            print(f"Info: Skipping GDGS calculation for well {las.well.WELL.value} due to missing GDRY or GSOLID.")

    # Get user input for the target log (now refers to the generic name)
    all_available_mnemonics = set()
    for las_item in las_files:
        all_available_mnemonics.update(las_item.curves.keys())

    generic_target_options = sorted(list(set(log_keywords.keys()) | all_available_mnemonics))

    selected_target_log_generic_name = get_target_log(generic_target_options, common_actual_mnemonics, common_generic_keywords)

    if not selected_target_log_generic_name:
        print("No target log selected. Exiting.")
        exit()

    found_in_at_least_one_well = False
    for las_item in las_files:
        temp_cols = find_default_columns(las_item, log_keywords)
        # Check if selected name is a direct mnemonic OR if it's a generic keyword that maps to an existing mnemonic
        if selected_target_log_generic_name in las_item.curves or \
           (selected_target_log_generic_name in temp_cols and temp_cols[selected_target_log_generic_name] in las_item.curves):
            found_in_at_least_one_well = True
            break
    if not found_in_at_least_one_well:
        print(f"Warning: Selected target log '{selected_target_log_generic_name}' does not appear to be a valid log in any of the loaded LAS files, either directly or via keyword mapping. Analysis might yield no results.")
        # Consider exiting or asking user to re-select
        # For now, proceed, but functions should handle missing target logs gracefully.

    # Prompt user to load Excel file with depth ranges upfront
    # Pass the LAS files to filter the Excel data to only include matching wells
    preloaded_excel_df = load_excel_depth_ranges(las_files)

    # Provide feedback if Excel data was loaded
    if preloaded_excel_df is not None:
        print(f"Excel file with depth ranges loaded successfully.")
        print(f"Found {len(preloaded_excel_df)} boundary entries for {preloaded_excel_df['Well'].nunique()} wells.")
        print("This data will be automatically used in the depth ranges dialog.")

    # Get depth ranges, passing the preloaded Excel data if available
    depth_ranges = get_depth_ranges(las_files, log_keywords, preloaded_excel_df)


    # Get calculation method and k method/value from user
    calcmethod, k_method, k_value = get_calcmethod_and_k_method()
    if calcmethod is None:
        print("No calcmethod selected. Exiting.")
        exit()

    analysis_type = simpledialog.askstring("Analysis Type", "Enter '1' for individual well analysis or '2' for merged analysis:")
    if analysis_type is None:
        print("No analysis type selected. Exiting.")
        exit()

    all_wells_data_for_plotting = []

    if analysis_type == '1':
        all_wells_results, all_wells_data_for_plotting = individual_well_analysis(
            las_files, log_keywords, selected_target_log_generic_name, depth_ranges, calcmethod, k_method, k_value, alternative_mnemonics
        )

        valid_results_for_summary = [res for res in all_wells_results if res.get('optimum_angle') is not None and res.get('max_correlation') is not None]

        if not valid_results_for_summary:
            print("No valid results from individual well analysis to plot summary chart.")
        else:
            fig, ax1 = plt.subplots(figsize=(14, 8))
            well_names_summary = [result['well_name'] for result in valid_results_for_summary]
            optimum_angles_summary = [result['optimum_angle'] for result in valid_results_for_summary]
            max_correlations_summary = [result['max_correlation'] for result in valid_results_for_summary]

            ax1.set_xlabel('Well Name')
            ax1.set_ylabel('Optimum Angle (degrees)', color='tab:blue')
            ax1.bar(well_names_summary, optimum_angles_summary, color='tab:blue', alpha=0.7)
            ax1.tick_params(axis='y', labelcolor='tab:blue')

            ax2 = ax1.twinx()
            ax2.set_ylabel('Max Correlation Coefficient', color='tab:orange')
            ax2.plot(well_names_summary, max_correlations_summary, color='tab:orange', marker='o')
            ax2.tick_params(axis='y', labelcolor='tab:orange')

            for i, result in enumerate(valid_results_for_summary):
                ax1.text(i, result['optimum_angle'],
                         f"{result['optimum_angle']:.1f}°\n{result['max_correlation']:.3f}\n{result['top_depth']:.0f}-{result['bottom_depth']:.0f}",
                         ha='center', va='bottom', fontweight='bold', fontsize=8)

            plt.title(f'Optimum Angle and Max Correlation for EEI-{selected_target_log_generic_name}\nDepth ranges vary by well')
            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()
            plt.show()

    elif analysis_type == '2':
        all_wells_data_for_plotting = merged_well_analysis(
            las_files, log_keywords, selected_target_log_generic_name, depth_ranges, calcmethod, k_method, k_value, alternative_mnemonics
        )

    else:
        print("Invalid analysis type selected. Exiting.")
        exit()

    final_plottable_wells_data = [
        data for data in all_wells_data_for_plotting
        if data.get('depth') is not None and
           data.get('target') is not None and
           data.get('normalized_eei') is not None
    ]

    if not final_plottable_wells_data:
        print("No plottable data available after analysis. Check logs and depth ranges.")
    else:
        plot_eei_vs_target(final_plottable_wells_data, selected_target_log_generic_name, depth_ranges)

    # Print summary of alternative mnemonics used
    if alternative_mnemonics:
        print("\n" + "="*80)
        print("ALTERNATIVE MNEMONICS SUMMARY")
        print("="*80)
        print("{:<20} {:<20} {:<20} {:<20}".format("Well Name", "Target Log", "Alternative Used", "Status"))
        print("-"*80)

        for well_key, alternative in alternative_mnemonics.items():
            well_name, target_name = well_key.split(":")
            status = "USED" if alternative else "SKIPPED"
            alternative_display = alternative if alternative else "N/A"
            print("{:<20} {:<20} {:<20} {:<20}".format(well_name, target_name, alternative_display, status))

        print("="*80)

    print("Analysis complete.")