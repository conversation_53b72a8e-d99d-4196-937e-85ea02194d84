# Calculator Function Integration Plan for EEI Analysis Workflow

## **Overview**
This document outlines the integration of the calculator function from [`b3_Xplot_HIST_KDE_FUNCT_Custom.py`](b3_Xplot_HIST_KDE_FUNCT_Custom.py:1123-1249) into [`a3_load_multilas_EEI_XCOR_PLOT_Final.py`](a3_load_multilas_EEI_XCOR_PLOT_Final.py) with enhanced error handling for missing log inputs.

## **Integration Point**
Insert the calculator function in the main workflow after existing derived log calculations (line 2335) and before target log selection (line 2336).

## **Workflow Sequence**
```mermaid
graph TD
    A[Load LAS Files] --> B[Validate Essential Logs]
    B --> C[Categorize Log Curves]
    C --> D[Calculate Existing Derived Logs]
    D --> E[**NEW: Optional Calculator Function**]
    E --> F[Get Target Log Selection]
    F --> G[Continue with EEI Analysis...]
    
    E --> E1[Show Calculator Dialog]
    E1 --> E2[User Creates Custom Logs]
    E2 --> E3[Validate Log Availability]
    E3 --> E4{All Logs Available?}
    E4 -->|Yes| E5[Apply Calculations to All Wells]
    E4 -->|No| E6[Show Detailed Error Message]
    E6 --> E7[User Chooses: Retry/Skip/Cancel]
    E7 -->|Retry| E1
    E7 -->|Skip| F
    E7 -->|Cancel| F
    E5 --> F
```

## **Enhanced Error Handling System**

### **1. Pre-Calculation Validation**
Before executing any calculations, validate that all referenced logs exist across all wells:

```python
def validate_calculation_inputs(las_files, calculation_text):
    """
    Validate that all logs referenced in calculations exist in all wells.
    
    Returns:
        dict: {
            'valid': bool,
            'missing_logs': dict,  # {well_name: [missing_log_names]}
            'available_logs': list,
            'error_details': str
        }
    """
    # Extract variable names from calculation text
    # Check availability across all wells
    # Generate detailed error report
```

### **2. Detailed Error Messages**
When logs are missing, provide comprehensive error information:

#### **Error Message Format**
```
CALCULATION ERROR: Missing Required Logs

The following logs referenced in your calculations are not available:

Well: WELL_001
  ❌ Missing: DTS, NPHI
  ✅ Available: DT, RHOB, GR, PHIE

Well: WELL_002  
  ❌ Missing: DTS
  ✅ Available: DT, RHOB, GR, PHIE, NPHI

Summary:
- DTS: Missing in 2/2 wells
- NPHI: Missing in 1/2 wells

Suggestions:
1. Check if these logs exist under different names (aliases)
2. Remove references to missing logs from calculations
3. Use alternative logs that are available in all wells

Available logs in ALL wells:
DT, RHOB, GR, PHIE

Would you like to:
[Retry] - Modify your calculations
[Skip] - Continue without custom calculations  
[Cancel] - Return to main workflow
```

### **3. Error Recovery Options**
Provide multiple recovery paths when errors occur:

```python
def handle_calculation_error(error_details, las_files):
    """
    Show error dialog with recovery options.
    
    Returns:
        str: 'retry', 'skip', or 'cancel'
    """
    root = tk.Tk()
    root.withdraw()
    
    # Create detailed error dialog
    error_dialog = tk.Toplevel()
    error_dialog.title("Calculation Error - Missing Logs")
    error_dialog.geometry("600x500")
    
    # Error details display
    error_text = tk.Text(error_dialog, wrap=tk.WORD, height=20)
    error_text.insert(tk.END, error_details['error_message'])
    error_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # Recovery buttons
    button_frame = tk.Frame(error_dialog)
    button_frame.pack(pady=10)
    
    result = {'action': 'cancel'}
    
    def on_retry():
        result['action'] = 'retry'
        error_dialog.destroy()
    
    def on_skip():
        result['action'] = 'skip'
        error_dialog.destroy()
    
    def on_cancel():
        result['action'] = 'cancel'
        error_dialog.destroy()
    
    tk.Button(button_frame, text="Retry Calculations", 
              command=on_retry, bg='lightblue').pack(side=tk.LEFT, padx=5)
    tk.Button(button_frame, text="Skip Calculator", 
              command=on_skip, bg='lightyellow').pack(side=tk.LEFT, padx=5)
    tk.Button(button_frame, text="Cancel", 
              command=on_cancel, bg='lightcoral').pack(side=tk.LEFT, padx=5)
    
    error_dialog.wait_window()
    return result['action']
```

## **Implementation Details**

### **1. Enhanced Calculator Function**
```python
def get_calculations_for_eei(las_files):
    """
    Enhanced calculator function with comprehensive error handling.
    """
    # Get columns that are present in all LAS files
    common_columns = set(las_files[0].curves.keys())
    for las in las_files[1:]:
        common_columns.intersection_update(las.curves.keys())
    columns = sorted(common_columns)

    if not columns:
        messagebox.showerror("Error", "No common columns found across all LAS files.")
        return False

    while True:
        # Show calculator interface
        calculations = show_calculator_interface(columns)
        
        if not calculations:  # User cancelled
            return False
        
        # Validate inputs before execution
        validation_result = validate_calculation_inputs(las_files, calculations)
        
        if not validation_result['valid']:
            # Show detailed error message
            action = handle_calculation_error(validation_result, las_files)
            
            if action == 'retry':
                continue  # Show calculator again
            elif action == 'skip':
                return False  # Skip calculator, continue workflow
            else:  # cancel
                return False
        
        # Execute calculations with additional error handling
        success = execute_calculations_safely(las_files, calculations)
        
        if success:
            return True
        else:
            # Handle execution errors
            action = handle_execution_error(las_files)
            if action == 'retry':
                continue
            else:
                return False
```

### **2. Safe Calculation Execution**
```python
def execute_calculations_safely(las_files, calculations):
    """
    Execute calculations with comprehensive error handling.
    """
    error_occurred = False
    error_details = []
    
    for i, las in enumerate(las_files):
        well_name = las.well.WELL.value
        
        # Prepare the execution environment
        local_ns = {}
        missing_logs = []
        
        # Check log availability for this well
        for curve in las.curves.keys():
            local_ns[curve] = np.array(las[curve].data)
        
        # Make numpy available
        local_ns['np'] = np
        
        try:
            # Execute the calculations
            exec(calculations, {}, local_ns)
        except NameError as e:
            # Handle missing variable errors
            missing_var = str(e).split("'")[1] if "'" in str(e) else "unknown"
            error_details.append(f"Well {well_name}: Missing log '{missing_var}'")
            error_occurred = True
            break
        except Exception as e:
            # Handle other execution errors
            error_details.append(f"Well {well_name}: {str(e)}")
            error_occurred = True
            break
        
        # Add new variables as curves to the LAS file
        for var_name, data in local_ns.items():
            if var_name not in las.curves.keys() and var_name != 'np':
                # Check if data is array-like and has the correct length
                if isinstance(data, np.ndarray) and data.shape[0] == len(las['DEPTH'].data):
                    # Add the new curve
                    las.append_curve(var_name, data)
                    print(f"Added new curve '{var_name}' to well {well_name}")
                else:
                    # Skip variables that are scalars or arrays of incorrect length
                    continue
    
    if error_occurred:
        # Show execution error details
        error_message = "Calculation Execution Error:\n\n"
        for detail in error_details:
            error_message += f"• {detail}\n"
        
        messagebox.showerror("Calculation Error", error_message)
        return False
    
    return True
```

### **3. Log Availability Analysis**
```python
def analyze_log_availability(las_files):
    """
    Analyze which logs are available across all wells.
    
    Returns:
        dict: {
            'common_logs': list,      # Available in all wells
            'partial_logs': dict,     # Available in some wells
            'well_specific': dict     # Unique to specific wells
        }
    """
    all_logs = {}
    well_count = len(las_files)
    
    # Count log occurrences across wells
    for las in las_files:
        well_name = las.well.WELL.value
        for curve_name in las.curves.keys():
            if curve_name not in all_logs:
                all_logs[curve_name] = {'wells': [], 'count': 0}
            all_logs[curve_name]['wells'].append(well_name)
            all_logs[curve_name]['count'] += 1
    
    # Categorize logs
    common_logs = [log for log, info in all_logs.items() if info['count'] == well_count]
    partial_logs = {log: info for log, info in all_logs.items() 
                   if 0 < info['count'] < well_count}
    
    return {
        'common_logs': sorted(common_logs),
        'partial_logs': partial_logs,
        'total_wells': well_count
    }
```

## **Enhanced User Interface**

### **1. Calculator Interface with Log Availability Info**
```python
def show_calculator_interface(columns):
    """
    Enhanced calculator interface showing log availability.
    """
    root = tk.Tk()
    root.title("Custom Log Calculator - EEI Analysis")
    root.geometry("900x700")
    
    # Create PanedWindow
    paned = ttk.PanedWindow(root, orient=tk.HORIZONTAL)
    paned.pack(fill=tk.BOTH, expand=True)
    
    # Left Frame: Available variables with availability info
    left_frame = ttk.Frame(paned, width=250)
    paned.add(left_frame, weight=0)
    
    # Enhanced variable list with availability indicators
    variables_label = tk.Label(left_frame, text="Available Variables:", anchor='w')
    variables_label.pack(fill=tk.X)
    
    # Add availability legend
    legend_frame = tk.Frame(left_frame)
    legend_frame.pack(fill=tk.X, pady=5)
    tk.Label(legend_frame, text="✅ Available in all wells", 
             fg='green', font=('Arial', 8)).pack(anchor='w')
    tk.Label(legend_frame, text="⚠️ Available in some wells", 
             fg='orange', font=('Arial', 8)).pack(anchor='w')
    
    variables_listbox = tk.Listbox(left_frame)
    
    # Analyze log availability
    log_analysis = analyze_log_availability(las_files)
    
    # Populate listbox with availability indicators
    for idx, col in enumerate(columns, start=1):
        if col in log_analysis['common_logs']:
            display_text = f"✅ ({idx}) {col}"
            variables_listbox.insert(tk.END, display_text)
        else:
            wells_with_log = log_analysis['partial_logs'].get(col, {}).get('wells', [])
            well_count = len(wells_with_log)
            total_wells = log_analysis['total_wells']
            display_text = f"⚠️ ({idx}) {col} [{well_count}/{total_wells} wells]"
            variables_listbox.insert(tk.END, display_text)
    
    variables_listbox.pack(fill=tk.BOTH, expand=True)
    
    # Right Frame: Instructions and Text widget
    right_frame = ttk.Frame(paned)
    paned.add(right_frame, weight=1)
    
    # Enhanced instructions with EEI examples
    instructions = (
        "Create custom calculated logs for EEI analysis.\n"
        "Use existing curve names as variables (✅ = available in all wells).\n\n"
        "EEI-Relevant Examples:\n"
        "PHIE_HC = PHIE * (1 - SWE)  # Effective porosity with hydrocarbons\n"
        "VP_VS_RATIO = (304800/DT) / (304800/DTS)  # Vp/Vs ratio\n"
        "AI = RHOB * (304800/DT)  # Acoustic impedance\n"
        "POISSON = 0.5 * ((VP_VS_RATIO**2 - 2) / (VP_VS_RATIO**2 - 1))\n\n"
        "Use numpy functions: np.log(), np.sqrt(), np.exp(), etc.\n"
        "⚠️ Warning: Using logs not available in all wells will cause errors!"
    )
    
    instruction_label = tk.Label(right_frame, text=instructions, 
                                justify=tk.LEFT, wraplength=600)
    instruction_label.pack(anchor='w', pady=10)
    
    # Text widget for calculations
    text = tk.Text(right_frame, width=80, height=20)
    text.pack(fill=tk.BOTH, expand=True)
    
    # Enhanced button frame
    button_frame = tk.Frame(root)
    button_frame.pack(pady=10)
    
    result = {"calculations": None}
    
    def on_submit():
        result["calculations"] = text.get("1.0", tk.END)
        root.quit()
    
    def on_check_syntax():
        calc_text = text.get("1.0", tk.END)
        try:
            compile(calc_text, '<string>', 'exec')
            messagebox.showinfo("Syntax Check", "✅ No syntax errors found.")
        except SyntaxError as e:
            messagebox.showerror("Syntax Error", f"❌ Syntax error:\n{e}")
    
    def on_check_availability():
        calc_text = text.get("1.0", tk.END)
        validation_result = validate_calculation_inputs(las_files, calc_text)
        
        if validation_result['valid']:
            messagebox.showinfo("Log Availability", 
                              "✅ All referenced logs are available in all wells.")
        else:
            messagebox.showwarning("Log Availability", 
                                 validation_result['error_details'])
    
    # Enhanced buttons
    tk.Button(button_frame, text="Check Syntax", 
              command=on_check_syntax, bg='lightblue').pack(side=tk.LEFT, padx=5)
    tk.Button(button_frame, text="Check Log Availability", 
              command=on_check_availability, bg='lightyellow').pack(side=tk.LEFT, padx=5)
    tk.Button(button_frame, text="Submit Calculations", 
              command=on_submit, bg='lightgreen').pack(side=tk.LEFT, padx=5)
    tk.Button(button_frame, text="Cancel", 
              command=root.quit, bg='lightcoral').pack(side=tk.LEFT, padx=5)
    
    root.mainloop()
    root.destroy()
    
    return result["calculations"]
```

## **Main Workflow Integration**

### **Integration Code**
```python
# Insert after line 2335 (existing derived log calculations)
# Enhanced calculator integration with error handling

print("Checking if user wants to create custom calculated logs...")

root = tk.Tk()
root.withdraw()
use_calculator = messagebox.askyesno(
    "Custom Log Calculator",
    "Would you like to create custom calculated logs before selecting the target log?\n\n"
    "This allows you to create new logs using mathematical operations on existing curves.\n"
    "Examples: porosity combinations, elastic ratios, normalized logs, etc.\n\n"
    "Note: Only logs available in ALL wells can be used safely."
)

if use_calculator:
    print("Opening enhanced calculator for custom log creation...")
    
    # Show log availability summary first
    log_analysis = analyze_log_availability(las_files)
    summary_message = (
        f"Log Availability Summary:\n\n"
        f"✅ Available in all {log_analysis['total_wells']} wells: {len(log_analysis['common_logs'])} logs\n"
        f"⚠️ Available in some wells: {len(log_analysis['partial_logs'])} logs\n\n"
        f"For safe calculations, use only logs marked with ✅ in the calculator.\n"
        f"Using ⚠️ logs may cause errors in wells where they're missing."
    )
    
    messagebox.showinfo("Log Availability Summary", summary_message)
    
    # Run enhanced calculator with error handling
    calculator_success = get_calculations_for_eei(las_files)
    
    if calculator_success:
        print("✅ Custom logs created successfully.")
        
        # Update log categories to include new logs
        log_categories = categorize_log_curves(las_files)
        print("Updated log inventory with new calculated logs.")
        
        # Optionally show updated inventory
        show_updated = messagebox.askyesno(
            "Calculator Complete", 
            "Custom logs have been created successfully!\n\n"
            "Would you like to see the updated log inventory?"
        )
        
        if show_updated:
            display_log_inventory(log_categories)
    else:
        print("ℹ️ Calculator operation cancelled or failed. Continuing with existing logs.")

# Continue with existing target log selection (line 2336)
print("Proceeding to target log selection...")
```

## **Error Scenarios and Handling**

### **1. Missing Log Error**
```
Scenario: User references 'DTS' but it's only available in 2 out of 3 wells
Error: NameError: name 'DTS' is not defined (in well WELL_003)
Handling: Pre-validation catches this, shows detailed availability report
```

### **2. Syntax Error**
```
Scenario: User types 'NEW_LOG = DT *' (incomplete expression)
Error: SyntaxError: invalid syntax
Handling: Syntax checker catches this before execution
```

### **3. Runtime Error**
```
Scenario: User tries 'LOG_RATIO = DT / ZERO_LOG' where ZERO_LOG contains zeros
Error: RuntimeWarning: divide by zero encountered
Handling: Try-catch during execution, show specific error message
```

### **4. Data Type Error**
```
Scenario: User creates scalar instead of array
Error: Array length mismatch when adding to LAS file
Handling: Validate array dimensions before adding curves
```

## **Benefits of Enhanced Error Handling**

1. **Proactive Validation**: Catch errors before execution
2. **Detailed Diagnostics**: Show exactly which logs are missing from which wells
3. **Multiple Recovery Options**: Retry, skip, or cancel based on user preference
4. **Educational**: Help users understand log availability across wells
5. **Robust Workflow**: Errors don't break the main EEI analysis workflow

## **Testing Scenarios**

### **Test Case 1: All Logs Available**
- User creates calculation using only common logs
- Should execute successfully across all wells
- New logs should appear in target selection

### **Test Case 2: Missing Logs**
- User references log not available in all wells
- Should show detailed error message with well-specific information
- Should offer retry option

### **Test Case 3: Syntax Error**
- User enters invalid Python syntax
- Should catch error during syntax check
- Should allow correction and retry

### **Test Case 4: Runtime Error**
- User creates calculation that fails during execution
- Should show specific error message
- Should not corrupt existing LAS data

## **Implementation Priority**

1. **Phase 1**: Basic integration with simple error handling
2. **Phase 2**: Enhanced error messages and recovery options
3. **Phase 3**: Log availability analysis and pre-validation
4. **Phase 4**: Advanced UI enhancements and user guidance

This comprehensive error handling system ensures that the calculator function integrates smoothly into the EEI workflow while providing users with clear, actionable feedback when issues arise.